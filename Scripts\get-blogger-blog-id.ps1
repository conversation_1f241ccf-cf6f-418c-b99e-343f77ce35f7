# Get Blogger Blog ID Helper Script
# This script helps you find your Blogger Blog ID using the Blogger API

param(
    [string]$BlogUrl = "https://elthosrpg.blogspot.com",
    [string]$NgrokUrl = ""
)

Write-Host "🔍 Blogger Blog ID Finder" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Get current nGrok URL if not provided
if (-not $NgrokUrl) {
    try {
        $NgrokApi = Invoke-RestMethod -Uri "http://127.0.0.1:4040/api/tunnels" -ErrorAction Stop
        $NgrokUrl = $NgrokApi.tunnels[0].public_url
        Write-Host "🔍 Detected nGrok URL: $NgrokUrl" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Could not detect nGrok URL. Please provide it manually." -ForegroundColor Yellow
        $NgrokUrl = Read-Host "Enter your current nGrok URL (e.g., https://abc123.ngrok-free.app)"
    }
}

Write-Host "`n📋 Step 1: Find Your Blog ID" -ForegroundColor Yellow
Write-Host "There are several ways to find your Blogger Blog ID:" -ForegroundColor White

Write-Host "`n🔗 Method 1: From Blogger Dashboard" -ForegroundColor Green
Write-Host "1. Go to: https://www.blogger.com/" -ForegroundColor White
Write-Host "2. Select your blog: Elthos RPG" -ForegroundColor White
Write-Host "3. Look at the URL in your browser address bar" -ForegroundColor White
Write-Host "4. The Blog ID is the long number in the URL after 'blogID='" -ForegroundColor White
Write-Host "   Example: https://www.blogger.com/blogger.g?blogID=1234567890123456789" -ForegroundColor Cyan
Write-Host "   Blog ID would be: 1234567890123456789" -ForegroundColor Cyan

Write-Host "`n🔗 Method 2: Using Blogger API (Recommended)" -ForegroundColor Green
Write-Host "1. Set up OAuth2 credentials in N8N (see setup-blogger-credentials.ps1)" -ForegroundColor White
Write-Host "2. Create a test workflow with HTTP Request node:" -ForegroundColor White
Write-Host "   Method: GET" -ForegroundColor White
Write-Host "   URL: https://www.googleapis.com/blogger/v3/users/self/blogs" -ForegroundColor Cyan
Write-Host "   Authentication: Use your Google OAuth2 credential" -ForegroundColor White
Write-Host "3. Execute the workflow" -ForegroundColor White
Write-Host "4. Look for your blog in the response and copy the 'id' field" -ForegroundColor White

Write-Host "`n🔗 Method 3: From Blog Source Code" -ForegroundColor Green
Write-Host "1. Go to your blog: $BlogUrl" -ForegroundColor White
Write-Host "2. View page source (Ctrl+U)" -ForegroundColor White
Write-Host "3. Search for 'blogId' or 'blog-id'" -ForegroundColor White
Write-Host "4. Look for a long numeric ID" -ForegroundColor White

Write-Host "`n📝 Example API Response Structure:" -ForegroundColor Yellow
Write-Host @"
{
  "kind": "blogger#blogList",
  "items": [
    {
      "kind": "blogger#blog",
      "id": "1234567890123456789",
      "name": "Elthos RPG",
      "description": "Your blog description",
      "published": "2010-01-01T00:00:00.000Z",
      "updated": "2025-01-01T00:00:00.000Z",
      "url": "https://elthosrpg.blogspot.com/",
      "selfLink": "https://www.googleapis.com/blogger/v3/blogs/1234567890123456789",
      "posts": {
        "totalItems": 150,
        "selfLink": "https://www.googleapis.com/blogger/v3/blogs/1234567890123456789/posts"
      }
    }
  ]
}
"@ -ForegroundColor Cyan

Write-Host "`n📋 Step 2: Update Your Workflow" -ForegroundColor Yellow
Write-Host "Once you have your Blog ID:" -ForegroundColor White
Write-Host "1. Open your workflow: ElthosRPG_Blog_Twitter_BloggerAPI.json" -ForegroundColor White
Write-Host "2. Find the 'Set Blog ID' node" -ForegroundColor White
Write-Host "3. Replace 'YOUR_BLOG_ID_HERE' with your actual Blog ID" -ForegroundColor White
Write-Host "4. Update the OAuth2 credential ID in 'Get Blog Posts via API' node" -ForegroundColor White

Write-Host "`n🔧 Test API Endpoints:" -ForegroundColor Green
Write-Host "After setup, you can test these endpoints:" -ForegroundColor White
Write-Host "• List blogs: https://www.googleapis.com/blogger/v3/users/self/blogs" -ForegroundColor Cyan
Write-Host "• List posts: https://www.googleapis.com/blogger/v3/blogs/{blogId}/posts" -ForegroundColor Cyan
Write-Host "• Get specific post: https://www.googleapis.com/blogger/v3/blogs/{blogId}/posts/{postId}" -ForegroundColor Cyan

Write-Host "`n💡 Pro Tips:" -ForegroundColor Green
Write-Host "• Blog IDs are long numeric strings (usually 19 digits)" -ForegroundColor White
Write-Host "• The same OAuth2 credential works for all Google APIs" -ForegroundColor White
Write-Host "• Use maxResults parameter to control how many posts are returned" -ForegroundColor White
Write-Host "• Add status=LIVE to only get published posts" -ForegroundColor White

Write-Host "`n🔗 Useful Links:" -ForegroundColor Blue
Write-Host "• Blogger Dashboard: https://www.blogger.com/" -ForegroundColor White
Write-Host "• Blogger API Documentation: https://developers.google.com/blogger/docs/3.0/reference/" -ForegroundColor White
Write-Host "• OAuth 2.0 Playground: https://developers.google.com/oauthplayground/" -ForegroundColor White
Write-Host "• Your blog: $BlogUrl" -ForegroundColor White
Write-Host "• N8N Interface: $NgrokUrl" -ForegroundColor White

Write-Host "`n✅ Next Steps:" -ForegroundColor Green
Write-Host "1. Find your Blog ID using one of the methods above" -ForegroundColor White
Write-Host "2. Update the workflow with your Blog ID" -ForegroundColor White
Write-Host "3. Test the 'Get Blog Posts via API' node" -ForegroundColor White
Write-Host "4. Run the complete workflow to verify it works" -ForegroundColor White

Write-Host "`n🎯 Expected Benefits:" -ForegroundColor Magenta
Write-Host "• More reliable than web scraping" -ForegroundColor White
Write-Host "• Gets structured data with metadata" -ForegroundColor White
Write-Host "• No dependency on HTML structure changes" -ForegroundColor White
Write-Host "• Access to post titles, dates, and other metadata" -ForegroundColor White
Write-Host "• Better error handling and debugging" -ForegroundColor White
