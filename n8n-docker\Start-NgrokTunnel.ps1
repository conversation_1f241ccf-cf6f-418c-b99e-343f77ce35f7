# Start-NgrokTunnel.ps1
# PowerShell wrapper for the Python ngrok tunnel script
# Handles virtual environment activation and parameter passing

param(
    [string]$Subdomain = "",
    [int]$Port = 5678,
    [switch]$SkipN8nCheck,
    [switch]$Verbose,
    [switch]$NoMonitor,
    [string]$VenvPath = "..\venv"
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-VirtualEnvironment {
    param([string]$VenvPath)
    
    $activateScript = Join-Path $VenvPath "Scripts\Activate.ps1"
    $pythonExe = Join-Path $VenvPath "Scripts\python.exe"
    
    return (Test-Path $activateScript) -and (Test-Path $pythonExe)
}

function Install-Requirements {
    param([string]$VenvPath)
    
    Write-ColorOutput "📦 Installing required packages..." -Color $Cyan
    
    $pythonExe = Join-Path $VenvPath "Scripts\python.exe"
    
    try {
        # Install pyngrok and requests
        & $pythonExe -m pip install pyngrok requests
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Packages installed successfully" -Color $Green
            return $true
        } else {
            Write-ColorOutput "❌ Failed to install packages" -Color $Red
            return $false
        }
    } catch {
        Write-ColorOutput "❌ Error installing packages: $($_.Exception.Message)" -Color $Red
        return $false
    }
}

function Test-PythonPackages {
    param([string]$VenvPath)
    
    $pythonExe = Join-Path $VenvPath "Scripts\python.exe"
    
    try {
        # Test if required packages are installed
        $result = & $pythonExe -c "import pyngrok, requests; print('OK')" 2>$null
        return $result -eq "OK"
    } catch {
        return $false
    }
}

# Main Script
Write-ColorOutput "🌐 Ngrok Tunnel Startup Script for n8n" -Color $Cyan
Write-ColorOutput "=====================================" -Color $Cyan

# Check if virtual environment exists
if (-not (Test-VirtualEnvironment $VenvPath)) {
    Write-ColorOutput "❌ Virtual environment not found at: $VenvPath" -Color $Red
    Write-ColorOutput "   Please create a virtual environment first:" -Color $Yellow
    Write-ColorOutput "   python -m venv $VenvPath" -Color $Yellow
    exit 1
}

Write-ColorOutput "✅ Virtual environment found" -Color $Green

# Check if required packages are installed
if (-not (Test-PythonPackages $VenvPath)) {
    Write-ColorOutput "⚠️  Required packages not found, installing..." -Color $Yellow
    
    if (-not (Install-Requirements $VenvPath)) {
        Write-ColorOutput "❌ Failed to install required packages" -Color $Red
        Write-ColorOutput "   Try manually: pip install pyngrok requests" -Color $Yellow
        exit 1
    }
}

Write-ColorOutput "✅ Required packages are available" -Color $Green

# Build Python script arguments
$pythonArgs = @()

if ($Port -ne 5678) {
    $pythonArgs += "--port", $Port
}

if ($Subdomain -ne "") {
    $pythonArgs += "--subdomain", $Subdomain
}

if ($SkipN8nCheck) {
    $pythonArgs += "--skip-n8n-check"
}

if ($Verbose) {
    $pythonArgs += "--verbose"
}

if ($NoMonitor) {
    $pythonArgs += "--no-monitor"
}

# Display configuration
Write-ColorOutput "📋 Configuration:" -Color $Cyan
Write-ColorOutput "   Virtual Environment: $VenvPath" -Color $Yellow
Write-ColorOutput "   Port: $Port" -Color $Yellow
if ($Subdomain -ne "") {
    Write-ColorOutput "   Subdomain: $Subdomain" -Color $Yellow
}
Write-ColorOutput "   Skip n8n Check: $SkipN8nCheck" -Color $Yellow
Write-ColorOutput "   Verbose: $Verbose" -Color $Yellow
Write-ColorOutput "   No Monitor: $NoMonitor" -Color $Yellow

Write-ColorOutput "" -Color $White
Write-ColorOutput "🚀 Starting Python ngrok tunnel script..." -Color $Cyan

# Run the Python script
try {
    $pythonExe = Join-Path $VenvPath "Scripts\python.exe"
    $pythonScript = "start_ngrok_tunnel.py"
    
    if (-not (Test-Path $pythonScript)) {
        Write-ColorOutput "❌ Python script not found: $pythonScript" -Color $Red
        exit 1
    }
    
    # Execute the Python script
    if ($pythonArgs.Count -gt 0) {
        & $pythonExe $pythonScript @pythonArgs
    } else {
        & $pythonExe $pythonScript
    }
    
} catch {
    Write-ColorOutput "❌ Error running Python script: $($_.Exception.Message)" -Color $Red
    exit 1
}

Write-ColorOutput "🛑 Ngrok tunnel script ended" -Color $Yellow 