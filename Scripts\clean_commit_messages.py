#!/usr/bin/env python3
"""
Clean Commit Messages Script
Identifies and provides commands to rewrite commit messages that contain references to Self-Healer or KnowledgeBase
"""

import subprocess
import re
import json
from typing import List, Dict, Tuple

def run_git_command(command: str) -> str:
    """Run a git command and return the output"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error: {e.stderr}")
        return ""

def get_all_commits() -> List[Dict]:
    """Get all commits with their hashes and messages"""
    # Get commit hash and message for all commits
    command = 'git log --pretty=format:"%H|%s" --all'
    output = run_git_command(command)
    
    commits = []
    for line in output.split('\n'):
        if '|' in line:
            hash_part, message = line.split('|', 1)
            commits.append({
                'hash': hash_part,
                'message': message,
                'original_message': message
            })
    
    return commits

def find_problematic_commits(commits: List[Dict]) -> List[Dict]:
    """Find commits that contain Self-Healer or KnowledgeBase references"""
    problematic = []
    
    # Patterns to search for (case insensitive)
    patterns = [
        r'self[-_\s]?healer',
        r'knowledge[-_\s]?base',
        r'self[-_\s]?heal',
        r'healer',
    ]
    
    for commit in commits:
        message_lower = commit['message'].lower()
        for pattern in patterns:
            if re.search(pattern, message_lower):
                problematic.append(commit)
                break
    
    return problematic

def generate_generic_message(original_message: str) -> str:
    """Generate a generic replacement message"""
    
    # Common generic replacements
    replacements = {
        # Self-Healer specific
        r'self[-_\s]?healer(?:\s+and\s+knowledgebase)?(?:\s+integration)?(?:\s+updates?)?(?:\s+components?)?': 'system components',
        r'self[-_\s]?healer(?:\s+&\s+knowledgebase)?(?:\s+updates?)?(?:\s+\d+)?': 'system updates',
        r'self[-_\s]?healer(?:\s+finalized)?(?:\s+and\s+integrated)?': 'system integration',
        r'self[-_\s]?healer(?:\s+separation)?(?:\s+finalization)?': 'system organization',
        r'self[-_\s]?heal(?:er)?(?:\s+fix)?': 'system fix',
        
        # KnowledgeBase specific  
        r'knowledgebase(?:\s+table\s+structures?)?(?:\s+and\s+sp)?': 'database structures',
        r'knowledgebase(?:\s+updates?)?': 'database updates',
        r'knowledge[-_\s]?base(?:\s+integration)?': 'data integration',
        
        # Combined references
        r'(?:remove\s+(?:private\s+)?(?:additional\s+)?(?:self[-_\s]?healer\s+and\s+knowledgebase|knowledgebase\s+and\s+self[-_\s]?healer)(?:\s+components?)?(?:\s+from\s+public\s+repository)?(?:\s+development\s+files)?(?:\s+files)?(?:\s+r\.\.\.)?(?:\s+10)?(?:\s+\d+)?)': 'remove private development components',
        r'(?:enhanced?\s+\.gitignore\s+to\s+comprehensively\s+exclude\s+all\s+private\s+self[-_\s]?healer\s+and\s+knowledgebase\s+components?)': 'enhance .gitignore for private component exclusion',
        r'(?:updates?\s+for\s+self[-_\s]?healer\s+separation\s+finalization)': 'updates for system separation finalization',
        r'(?:update\s+gitignore\s+for\s+self[-_\s]?healer\s+and\s+knowledgebase)': 'update gitignore for private components',
        
        # Generic patterns
        r'fixes?\s+to\s+self[-_\s]?healer(?:\s+and\s+documentation)?': 'fixes to system and documentation',
        r'updates?\s+to\s+self[-_\s]?healer(?:\s*/\s*knowledgebase)?': 'updates to system components',
        r'update\s+to\s+full\s+version\s+of\s+self[-_\s]?healer': 'update to full system version',
    }
    
    # Apply replacements (case insensitive)
    new_message = original_message
    for pattern, replacement in replacements.items():
        new_message = re.sub(pattern, replacement, new_message, flags=re.IGNORECASE)
    
    # If no specific replacement was made, create a generic message
    if new_message.lower() == original_message.lower():
        if 'update' in original_message.lower():
            new_message = 'System updates and improvements'
        elif 'fix' in original_message.lower():
            new_message = 'System fixes and improvements'
        elif 'integration' in original_message.lower():
            new_message = 'System integration improvements'
        elif 'documentation' in original_message.lower():
            new_message = 'Documentation updates'
        else:
            new_message = 'System improvements and updates'
    
    return new_message

def generate_rewrite_script(problematic_commits: List[Dict]) -> str:
    """Generate a PowerShell script to rewrite commit messages"""
    
    script_lines = [
        "# PowerShell script to rewrite problematic commit messages",
        "# WARNING: This will rewrite Git history. Make sure you have backups!",
        "",
        "Write-Host 'Starting commit message cleanup...'",
        "Write-Host 'This will rewrite Git history - make sure you have backups!'",
        "",
        "# Create backup branch",
        "git branch backup-before-cleanup",
        "Write-Host 'Created backup branch: backup-before-cleanup'",
        "",
    ]
    
    # Generate filter-branch command for each problematic commit
    for i, commit in enumerate(problematic_commits):
        new_message = generate_generic_message(commit['message'])
        commit['new_message'] = new_message
        
        # Escape quotes for PowerShell
        escaped_old = commit['message'].replace('"', '""').replace("'", "''")
        escaped_new = new_message.replace('"', '""').replace("'", "''")
        
        script_lines.extend([
            f"# Commit {i+1}: {commit['hash'][:8]}",
            f"# Old: {escaped_old}",
            f"# New: {escaped_new}",
            f'$env:FILTER_BRANCH_SQUELCH_WARNING = 1',
            f'git filter-branch -f --msg-filter \'',
            f'    if ($_ -eq "{escaped_old}") {{',
            f'        "{escaped_new}"',
            f'    }} else {{',
            f'        $_',
            f'    }}',
            f'\' -- --all',
            "",
        ])
    
    script_lines.extend([
        "Write-Host 'Commit message cleanup completed!'",
        "Write-Host 'You may need to force push: git push --force-with-lease'",
        "Write-Host 'Backup branch available: backup-before-cleanup'",
    ])
    
    return '\n'.join(script_lines)

def main():
    print("🔍 Analyzing Git commit history for problematic references...")
    
    # Get all commits
    commits = get_all_commits()
    print(f"Found {len(commits)} total commits")
    
    # Find problematic commits
    problematic = find_problematic_commits(commits)
    print(f"Found {len(problematic)} commits with problematic references")
    
    if not problematic:
        print("✅ No problematic commit messages found!")
        return
    
    print("\n📋 Problematic commits found:")
    for i, commit in enumerate(problematic):
        print(f"{i+1}. {commit['hash'][:8]} - {commit['message']}")
        new_message = generate_generic_message(commit['message'])
        print(f"   → {new_message}")
        print()
    
    # Generate rewrite script
    script_content = generate_rewrite_script(problematic)
    
    # Save the script
    script_path = "rewrite_commit_messages.ps1"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ Generated rewrite script: {script_path}")
    print("\n⚠️  WARNING: This script will rewrite Git history!")
    print("📋 Next steps:")
    print("1. Review the generated script carefully")
    print("2. Make sure you have backups")
    print("3. Run: .\\Scripts\\rewrite_commit_messages.ps1")
    print("4. Force push: git push --force-with-lease")
    
    # Also save analysis results
    analysis_path = "Scripts/commit_analysis.json"
    with open(analysis_path, 'w', encoding='utf-8') as f:
        json.dump({
            'total_commits': len(commits),
            'problematic_commits': len(problematic),
            'commits': problematic
        }, f, indent=2)
    
    print(f"📊 Analysis saved to: {analysis_path}")

if __name__ == "__main__":
    main()
