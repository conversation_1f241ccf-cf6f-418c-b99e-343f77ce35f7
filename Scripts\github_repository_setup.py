#!/usr/bin/env python3
"""
GitHub Repository Setup for N8N_Builder Community Edition
=========================================================
Creates and configures a new GitHub repository for the public release.

Usage: python github_repository_setup.py [--repo-name NAME] [--dry-run] [--verbose]
"""

import os
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class GitHubRepositorySetup:
    """Setup GitHub repository for Community Edition release."""
    
    def __init__(self, repo_name: str = "N8N_Builder", dry_run: bool = False, verbose: bool = False):
        self.repo_name = repo_name
        self.dry_run = dry_run
        self.verbose = verbose
        self.setup_start_time = datetime.now()
        
        # Load configuration
        self.config = self.load_github_config()
        
        self.results = {
            "setup_metadata": {
                "start_time": self.setup_start_time.isoformat(),
                "repo_name": repo_name,
                "dry_run": dry_run,
                "components_created": [],
                "components_failed": []
            },
            "github_files": {}
        }
    
    def load_github_config(self) -> Dict[str, Any]:
        """Load GitHub repository configuration."""
        config_file = Path("Scripts/public_repo_config.json")
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                config = json.load(f)
                return config.get("github_repository_settings", {})
        
        # Default configuration
        return {
            "name": "N8N_Builder",
            "description": "🤖 AI-Powered Workflow Automation - Transform plain English into n8n workflows using local AI",
            "topics": [
                "n8n", "workflow-automation", "ai", "llm", "local-ai", "python", 
                "fastapi", "docker", "automation", "no-code", "low-code", 
                "workflow-generator", "lm-studio"
            ],
            "homepage": "",
            "license": "MIT",
            "default_branch": "main",
            "features": {
                "issues": True,
                "projects": True,
                "wiki": False,
                "discussions": True
            }
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def create_issue_templates(self) -> Dict[str, Any]:
        """Create GitHub issue templates."""
        component_name = "issue_templates"
        self.log("Creating GitHub issue templates...")
        
        result = {
            "component_name": component_name,
            "success": True,
            "files_created": []
        }
        
        # Create .github directory
        github_dir = Path(".github")
        issue_templates_dir = github_dir / "ISSUE_TEMPLATE"
        
        if not self.dry_run:
            issue_templates_dir.mkdir(parents=True, exist_ok=True)
        
        # Bug report template
        bug_report = """---
name: Bug report
about: Create a report to help us improve N8N_Builder
title: '[BUG] '
labels: 'bug'
assignees: ''
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
- OS: [e.g. Windows 10, macOS, Ubuntu]
- Python Version: [e.g. 3.8, 3.9, 3.10]
- N8N_Builder Version: [e.g. 1.0.0]
- LM Studio Version: [e.g. 0.2.9]

**Workflow Description**
If the bug is related to workflow generation, please provide:
- The natural language description you used
- The expected workflow behavior
- The actual workflow generated (if any)

**Additional context**
Add any other context about the problem here.
"""
        
        # Feature request template
        feature_request = """---
name: Feature request
about: Suggest an idea for N8N_Builder Community Edition
title: '[FEATURE] '
labels: 'enhancement'
assignees: ''
---

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Use Case**
Describe the specific use case or workflow scenario where this feature would be helpful.

**Additional context**
Add any other context or screenshots about the feature request here.

**Community Edition Scope**
Please confirm this feature request is appropriate for the Community Edition (open-source, local AI-powered workflow generation).
"""
        
        # Workflow help template
        workflow_help = """---
name: Workflow Generation Help
about: Get help with generating specific workflows
title: '[HELP] '
labels: 'help wanted, workflow'
assignees: ''
---

**Workflow Description**
Please describe the workflow you're trying to create in plain English.

**Current Attempt**
What description did you provide to N8N_Builder?

**Expected Outcome**
What kind of workflow were you expecting?

**Actual Result**
What did N8N_Builder generate instead? (Please paste the JSON or describe the issue)

**Environment**
- N8N_Builder Version: [e.g. 1.0.0]
- LM Studio Model: [e.g. mimo-vl-7b-rl]
- n8n Version: [e.g. 1.0.0]

**Additional Context**
Any additional information that might help us understand your use case.
"""
        
        templates = {
            "bug_report.md": bug_report,
            "feature_request.md": feature_request,
            "workflow_help.md": workflow_help
        }
        
        for filename, content in templates.items():
            file_path = issue_templates_dir / filename
            
            if not self.dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            result["files_created"].append(str(file_path))
            self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created issue template: {filename}")
        
        return result
    
    def create_pull_request_template(self) -> Dict[str, Any]:
        """Create GitHub pull request template."""
        component_name = "pull_request_template"
        self.log("Creating GitHub pull request template...")
        
        result = {
            "component_name": component_name,
            "success": True,
            "files_created": []
        }
        
        pr_template = """## Description
Brief description of the changes in this pull request.

## Type of Change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Testing
Please describe the tests that you ran to verify your changes.

- [ ] Existing tests pass
- [ ] New tests added (if applicable)
- [ ] Manual testing performed
- [ ] Workflow generation tested with various inputs

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes

## Community Edition Compatibility
- [ ] Changes are compatible with Community Edition scope
- [ ] No private/proprietary components introduced
- [ ] Local AI processing maintained
- [ ] Open-source dependencies only

## Additional Notes
Any additional information, screenshots, or context about the pull request.
"""
        
        github_dir = Path(".github")
        file_path = github_dir / "pull_request_template.md"
        
        if not self.dry_run:
            github_dir.mkdir(exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pr_template)
        
        result["files_created"].append(str(file_path))
        self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created pull request template")
        
        return result
    
    def create_contributing_guide(self) -> Dict[str, Any]:
        """Create contributing guidelines."""
        component_name = "contributing_guide"
        self.log("Creating contributing guidelines...")
        
        result = {
            "component_name": component_name,
            "success": True,
            "files_created": []
        }
        
        contributing_content = """# Contributing to N8N_Builder Community Edition

Thank you for your interest in contributing to N8N_Builder! This document provides guidelines for contributing to the Community Edition.

## 🎯 Community Edition Scope

N8N_Builder Community Edition focuses on:
- AI-powered workflow generation using local LLM models
- Open-source, privacy-focused automation
- Integration with n8n workflow platform
- Local processing with LM Studio
- REST API and web interface

## 🚀 Getting Started

### Prerequisites
- Python 3.8 or higher
- Git
- LM Studio with a compatible model
- n8n (for testing generated workflows)

### Development Setup
1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/N8N_Builder.git
   cd N8N_Builder
   ```
3. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\\Scripts\\activate
   ```
4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
5. Run tests:
   ```bash
   python -m pytest tests/ -v
   ```

## 📝 How to Contribute

### Reporting Bugs
- Use the bug report template
- Include detailed reproduction steps
- Provide environment information
- Include workflow descriptions if applicable

### Suggesting Features
- Use the feature request template
- Ensure the feature fits Community Edition scope
- Describe the use case clearly
- Consider implementation complexity

### Code Contributions

#### Branch Naming
- `feature/description` for new features
- `bugfix/description` for bug fixes
- `docs/description` for documentation updates

#### Coding Standards
- Follow PEP 8 for Python code
- Use type hints where appropriate
- Write docstrings for functions and classes
- Keep functions focused and small

#### Testing
- Write tests for new functionality
- Ensure existing tests pass
- Test workflow generation with various inputs
- Include integration tests where appropriate

#### Documentation
- Update README.md if needed
- Add docstrings to new functions
- Update API documentation
- Include examples for new features

## 🔍 Code Review Process

1. **Submit Pull Request**: Use the PR template
2. **Automated Checks**: Ensure CI passes
3. **Code Review**: Maintainers will review your code
4. **Address Feedback**: Make requested changes
5. **Merge**: Once approved, your PR will be merged

## 🧪 Testing Guidelines

### Unit Tests
```bash
python -m pytest tests/unit/ -v
```

### Integration Tests
```bash
python -m pytest tests/integration/ -v
```

### Workflow Generation Tests
```bash
python -m pytest tests/workflow/ -v
```

### Manual Testing
- Test with different LM Studio models
- Verify generated workflows work in n8n
- Test API endpoints
- Check web interface functionality

## 📚 Documentation

### API Documentation
- Use OpenAPI/Swagger standards
- Include request/response examples
- Document error codes and messages

### User Documentation
- Write clear, step-by-step instructions
- Include screenshots where helpful
- Provide troubleshooting guides
- Keep examples up-to-date

## 🎨 UI/UX Guidelines

### Web Interface
- Keep interface clean and intuitive
- Ensure responsive design
- Use consistent styling
- Provide clear feedback to users

### API Design
- Follow RESTful principles
- Use consistent naming conventions
- Provide comprehensive error messages
- Include proper HTTP status codes

## 🔒 Security Considerations

### Local Processing
- Maintain local AI processing
- No data sent to external services
- Secure credential handling
- Input validation and sanitization

### Dependencies
- Use only open-source dependencies
- Keep dependencies up-to-date
- Review security implications
- Avoid unnecessary dependencies

## 📋 Community Guidelines

### Communication
- Be respectful and inclusive
- Provide constructive feedback
- Help other contributors
- Ask questions when unclear

### Issue Management
- Use appropriate labels
- Provide detailed descriptions
- Follow up on your issues
- Close resolved issues

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- GitHub contributors page

## 📞 Getting Help

- **GitHub Discussions**: For questions and ideas
- **GitHub Issues**: For bugs and feature requests
- **Documentation**: Check existing docs first

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to N8N_Builder Community Edition! 🎉
"""
        
        file_path = Path("CONTRIBUTING.md")
        
        if not self.dry_run:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(contributing_content)
        
        result["files_created"].append(str(file_path))
        self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created contributing guidelines")
        
        return result
    
    def create_github_actions(self) -> Dict[str, Any]:
        """Create GitHub Actions workflows."""
        component_name = "github_actions"
        self.log("Creating GitHub Actions workflows...")
        
        result = {
            "component_name": component_name,
            "success": True,
            "files_created": []
        }
        
        # CI workflow
        ci_workflow = """name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8
    
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=n8n_builder --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit
    
    - name: Run safety check
      run: safety check
    
    - name: Run bandit security check
      run: bandit -r n8n_builder/
"""
        
        # Release workflow
        release_workflow = """name: Release

on:
  release:
    types: [published]

jobs:
  validate:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install build twine

    - name: Run tests
      run: |
        python -m pytest tests/ -v

    - name: Build package
      run: python -m build

    - name: Check package
      run: twine check dist/*

    - name: Validate Community Edition
      run: |
        python -c "import n8n_builder; print('✅ N8N_Builder Community Edition validated')"

    # Note: PyPI publishing would require secrets setup
    # - name: Publish to PyPI
    #   env:
    #     TWINE_USERNAME: __token__
    #     TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
    #   run: twine upload dist/*
"""
        
        workflows_dir = Path(".github/workflows")
        
        if not self.dry_run:
            workflows_dir.mkdir(parents=True, exist_ok=True)
        
        workflows = {
            "ci.yml": ci_workflow,
            "release.yml": release_workflow
        }
        
        for filename, content in workflows.items():
            file_path = workflows_dir / filename
            
            if not self.dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            result["files_created"].append(str(file_path))
            self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created GitHub Action: {filename}")
        
        return result
    
    def create_repository_files(self) -> Dict[str, Any]:
        """Create all GitHub repository files."""
        self.log("🚀 Creating GitHub repository setup files")
        
        components = [
            self.create_issue_templates,
            self.create_pull_request_template,
            self.create_contributing_guide,
            self.create_github_actions
        ]
        
        overall_success = True
        
        for component_method in components:
            try:
                component_result = component_method()
                component_name = component_result["component_name"]
                
                self.results["github_files"][component_name] = component_result
                
                if component_result["success"]:
                    self.results["setup_metadata"]["components_created"].append(component_name)
                    self.log(f"✅ Component '{component_name}' created successfully", "SUCCESS")
                else:
                    self.results["setup_metadata"]["components_failed"].append(component_name)
                    overall_success = False
                    self.log(f"❌ Component '{component_name}' failed", "ERROR")
            
            except Exception as e:
                self.log(f"❌ Component creation crashed: {component_method.__name__} - {e}", "ERROR")
                self.results["setup_metadata"]["components_failed"].append(component_method.__name__)
                overall_success = False
        
        # Update final metadata
        self.results["setup_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["setup_metadata"]["duration_seconds"] = (datetime.now() - self.setup_start_time).total_seconds()
        self.results["setup_metadata"]["overall_success"] = overall_success
        
        # Display summary
        self.display_setup_summary()
        
        return self.results
    
    def display_setup_summary(self):
        """Display setup summary."""
        print("\n" + "="*60)
        print("🚀 GITHUB REPOSITORY SETUP SUMMARY")
        print("="*60)
        
        metadata = self.results["setup_metadata"]
        total_components = len(metadata["components_created"]) + len(metadata["components_failed"])
        
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"📦 Components Created: {len(metadata['components_created'])}")
        print(f"❌ Components Failed: {len(metadata['components_failed'])}")
        
        if metadata["overall_success"]:
            print("\n🎉 GITHUB SETUP SUCCESSFUL!")
            print("✅ All repository files created")
            if not self.dry_run:
                print("🚀 Repository is ready for GitHub creation")
            else:
                print("ℹ️ This was a dry run - use without --dry-run to create files")
        else:
            print(f"\n⚠️ GITHUB SETUP INCOMPLETE")
            print("❌ Some components failed to create")
            
            if metadata["components_failed"]:
                print(f"\n📋 Failed Components:")
                for component in metadata["components_failed"]:
                    print(f"   - {component}")
        
        print("="*60)
    
    def export_results(self, output_file: str = "github_setup_results.json"):
        """Export setup results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Setup results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="GitHub Repository Setup")
    parser.add_argument("--repo-name", default="N8N_Builder", help="Repository name")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--output", default="github_setup_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize setup
    setup = GitHubRepositorySetup(args.repo_name, args.dry_run, args.verbose)
    
    # Create repository files
    results = setup.create_repository_files()
    
    # Export results
    setup.export_results(args.output)
    
    # Exit with appropriate code
    if results["setup_metadata"]["overall_success"]:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
