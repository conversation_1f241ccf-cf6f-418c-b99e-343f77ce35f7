# ============================================================================
# LOGS AND RUNTIME FILES
# ============================================================================
# Log files
logs/
*.log
*.log.*
*.out

# Runtime and temporary files
*.tmp
*.temp
*.bak
*.backup
*~

# ============================================================================
# CONFIGURATION AND SENSITIVE FILES
# ============================================================================
# Environment files
config.ps1
**/config.ps1

# nGrok configuration (contains auth tokens)
ngrok.yml
ngrok-config*.yml
**/ngrok.yml
**/ngrok-config*.yml

# ============================================================================
# DEVELOPMENT ENVIRONMENT FILES
# ============================================================================
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*.sublime-*

# OS-specific files
.DS_Store
Thumbs.db
desktop.ini

# Windows-specific reserved names
nul
CON
PRN
AUX

# ============================================================================
# PYTHON DEVELOPMENT FILES
# ============================================================================
# Python bytecode and compiled files
*.pyc
*.pyo
*.pyd
__pycache__/
*.so

# Python packaging
*.egg
*.egg-info/
build/
dist/
sdist/
wheels/
*.whl
develop-eggs/
downloads/
eggs/
parts/
var/
.installed.cfg
.Python

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# ============================================================================
# TESTING AND COVERAGE
# ============================================================================
# Testing files
.pytest_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/
.hypothesis/
.tox/
.nox/

# ============================================================================
# CACHING AND GENERATED FILES
# ============================================================================
# Cache directories and files
cache/
.cache/
*.cache
__pycache__/

# Generated analysis files
project_analysis_report.json
safe_project_analysis_*.json
safe_project_analysis_*.md

# Learning and AI model files
*.pkl
*.pickle
learning_data/
model_cache/

# ============================================================================
# NODE.JS AND WEB DEVELOPMENT
# ============================================================================
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Web assets
*.min.js
*.min.css

# ============================================================================
# PRIVATE/PROPRIETARY COMPONENTS (NEVER COMMIT TO PUBLIC)
# ============================================================================
# Private system directories
Self_Healer/
Self-Healer/
KnowledgeBase/
advanced_systems/

# Private configuration files
config_private.yaml
healer_config.yaml
knowledgebase_config.yaml
**/config_private.yaml
**/healer_config.yaml
**/knowledgebase_config.yaml

# Private development files
*_private_*
*_advanced_*
debug_self_healer*.py
**/debug_self_healer*.py

# Private test files and results
test_*self_healer*.py
test_*knowledgebase*.py
**/test_*self_healer*.py
**/test_*knowledgebase*.py
README_KnowledgeBase_Testing.md
pre_commit_cleanup_summary.json

# Private project folders
projects/knowledgebase*/
projects/self_healer*/
projects/advanced*/

# Private scripts and utilities
*knowledgebase*.ps1
*self_healer*.ps1
cleanup_duplicate_folder.ps1
cleanup_self_healer.ps1
test_smart_logging.py

# ============================================================================
# ADDITIONAL EXCLUSIONS FOR CLEAN REPOSITORY
# ============================================================================
# Database files
*.db
*.sqlite
*.sqlite3

# Compiled documentation
docs/_build/
site/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# PyCharm
.idea/

# VS Code workspace files
*.code-workspace

# Temporary directories
tmp/
temp/
