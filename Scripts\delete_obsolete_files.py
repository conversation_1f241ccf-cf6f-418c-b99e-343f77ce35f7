#!/usr/bin/env python3
"""
Delete Obsolete Documentation Files

Safely deletes the 60 obsolete files identified in the streamlined cleanup analysis.
All files are backed up in git, so this operation is reversible.

Author: N8N_Builder Development Team
Date: 2025-07-08
Purpose: Execute Step 3 of documentation consolidation
"""

import os
import json
from pathlib import Path
from datetime import datetime

def load_files_to_delete():
    """Load the list of files to delete from the analysis"""
    analysis_file = "data/streamlined_cleanup_analysis.json"
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis = json.load(f)
        return analysis['files_to_delete']
    except Exception as e:
        print(f"Error loading analysis file: {e}")
        return []

def delete_files_safely(files_to_delete):
    """Safely delete the specified files"""
    deleted_files = []
    failed_deletions = []
    
    print(f"Preparing to delete {len(files_to_delete)} obsolete files...")
    print("All files are backed up in git - this operation is reversible.\n")
    
    for file_path in files_to_delete:
        try:
            full_path = Path(file_path)
            if full_path.exists():
                # Delete the file
                full_path.unlink()
                deleted_files.append(file_path)
                print(f"✅ Deleted: {file_path}")
            else:
                print(f"⚠️  File not found (already deleted?): {file_path}")
        except Exception as e:
            failed_deletions.append({'file': file_path, 'error': str(e)})
            print(f"❌ Failed to delete {file_path}: {e}")
    
    return deleted_files, failed_deletions

def cleanup_empty_directories():
    """Remove empty directories after file deletion"""
    empty_dirs_removed = []
    
    # Check common directories that might be empty
    dirs_to_check = [
        "Documentation/api",
        "Documentation/guides", 
        "Documentation/technical",
        "n8n-docker/Documentation/guides",
        "n8n-docker/Documentation/technical",
        "n8n-docker/Documentation",
        "n8n-docker/legacy-tunneling",
        "n8n-docker/ssl",
        "projects/elthosdb1",
        "projects/test-1", 
        "projects/test-project",
        "Self_Healer/Documentation/DB_Admin",
        "Self_Healer/Documentation"
    ]
    
    for dir_path in dirs_to_check:
        try:
            full_path = Path(dir_path)
            if full_path.exists() and full_path.is_dir():
                # Check if directory is empty (only contains hidden files or is truly empty)
                contents = list(full_path.iterdir())
                if not contents:
                    full_path.rmdir()
                    empty_dirs_removed.append(dir_path)
                    print(f"🗂️  Removed empty directory: {dir_path}")
        except Exception as e:
            print(f"⚠️  Could not remove directory {dir_path}: {e}")
    
    return empty_dirs_removed

def generate_deletion_report(deleted_files, failed_deletions, empty_dirs_removed):
    """Generate a report of the deletion operation"""
    report_file = "data/file_deletion_report.md"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# File Deletion Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 📊 Summary\n\n")
        f.write(f"- **Files Successfully Deleted:** {len(deleted_files)}\n")
        f.write(f"- **Failed Deletions:** {len(failed_deletions)}\n")
        f.write(f"- **Empty Directories Removed:** {len(empty_dirs_removed)}\n\n")
        
        if deleted_files:
            f.write("## ✅ Successfully Deleted Files\n\n")
            for file_path in sorted(deleted_files):
                f.write(f"- `{file_path}`\n")
            f.write("\n")
        
        if failed_deletions:
            f.write("## ❌ Failed Deletions\n\n")
            for failure in failed_deletions:
                f.write(f"- `{failure['file']}`: {failure['error']}\n")
            f.write("\n")
        
        if empty_dirs_removed:
            f.write("## 🗂️ Empty Directories Removed\n\n")
            for dir_path in sorted(empty_dirs_removed):
                f.write(f"- `{dir_path}/`\n")
            f.write("\n")
        
        f.write("## 🔄 Recovery Instructions\n\n")
        f.write("All deleted files are backed up in git. To recover any file:\n\n")
        f.write("```bash\n")
        f.write("# To see what was deleted\n")
        f.write("git status\n\n")
        f.write("# To recover a specific file\n")
        f.write("git checkout HEAD -- path/to/file.md\n\n")
        f.write("# To recover all deleted files\n")
        f.write("git checkout HEAD -- .\n")
        f.write("```\n\n")
        
        f.write("## 🎯 Next Steps\n\n")
        f.write("1. Review the remaining documentation structure\n")
        f.write("2. Update cross-references in kept files\n")
        f.write("3. Test that all links work correctly\n")
        f.write("4. Commit the changes to git\n")
    
    return report_file

def main():
    """Main execution function"""
    print("N8N_Builder Documentation Cleanup - File Deletion")
    print("=" * 55)
    
    # Load files to delete
    files_to_delete = load_files_to_delete()
    if not files_to_delete:
        print("No files to delete found in analysis.")
        return
    
    print(f"Loaded {len(files_to_delete)} files to delete from analysis.")
    
    # Confirm deletion
    print("\n⚠️  WARNING: This will delete 60 obsolete documentation files.")
    print("All files are backed up in git and can be recovered.")
    
    response = input("\nProceed with deletion? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("Operation cancelled.")
        return
    
    # Delete files
    print("\nStarting file deletion...")
    deleted_files, failed_deletions = delete_files_safely(files_to_delete)
    
    # Clean up empty directories
    print("\nCleaning up empty directories...")
    empty_dirs_removed = cleanup_empty_directories()
    
    # Generate report
    print("\nGenerating deletion report...")
    report_file = generate_deletion_report(deleted_files, failed_deletions, empty_dirs_removed)
    
    # Summary
    print(f"\n📊 Deletion Complete!")
    print(f"- Successfully deleted: {len(deleted_files)} files")
    print(f"- Failed deletions: {len(failed_deletions)}")
    print(f"- Empty directories removed: {len(empty_dirs_removed)}")
    print(f"- Report saved to: {report_file}")
    
    if failed_deletions:
        print(f"\n⚠️  {len(failed_deletions)} files could not be deleted. Check the report for details.")
    else:
        print(f"\n✅ All files deleted successfully!")
    
    print(f"\n🔄 Files can be recovered using: git checkout HEAD -- <filename>")

if __name__ == "__main__":
    main()
