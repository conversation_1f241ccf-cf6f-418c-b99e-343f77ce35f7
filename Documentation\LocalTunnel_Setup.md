# LocalTunnel Setup for n8n OAuth Integrations

LocalTunnel provides **free, stable HTTPS URLs** for n8n OAuth integrations without requiring paid services like ngrok. This guide shows how to set up LocalTunnel specifically for your **n8n Docker instance** to enable OAuth2 integrations with services like Twitter, Google, GitHub, etc.

## Overview

**Perfect for**: OAuth2 callback URLs, webhook testing, API development
**Cost**: Completely free
**Requirements**: Node.js, Docker running n8n
**Security**: HTTPS enabled automatically

**Prerequisites**:
- ✅ **n8n running in Docker** (port 5678)
- ✅ **Node.js installed** (for LocalTunnel npm package)
- ✅ **Internet connection** for tunnel creation

> **Note**: This setup is independent of N8N_Builder. N8N_Builder is a separate workflow generation tool that creates JSON files. LocalTunnel works directly with your n8n Docker instance regardless of whether you use N8N_Builder or not.

## Setup Instructions

### Step 1: Install LocalTunnel

Navigate to your n8n-docker directory and install LocalTunnel locally:

```bash
cd n8n-docker
npm install localtunnel
```

### Step 2: Create Startup Script

Create `Start-LocalTunnel.ps1` in your n8n-docker directory:

```powershell
# LocalTunnel for n8n OAuth Integrations

Write-Host "🌐 LocalTunnel for n8n OAuth Integrations" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Configuration
$PORT = 5678
$SUBDOMAIN = "n8n-oauth-stable"
$PUBLIC_URL = "https://$SUBDOMAIN.loca.lt"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Local n8n: http://localhost:$PORT" -ForegroundColor White
Write-Host "  Public URL: $PUBLIC_URL" -ForegroundColor White
Write-Host "  OAuth Callback: $PUBLIC_URL/rest/oauth2-credential/callback" -ForegroundColor Cyan
Write-Host ""

# Check if n8n is running
Write-Host "Checking n8n status..." -ForegroundColor Yellow
$dockerStatus = docker-compose ps --services --filter "status=running" 2>$null
if ($dockerStatus -notcontains "n8n") {
    Write-Host "⚠️  n8n is not running. Starting n8n first..." -ForegroundColor Yellow
    docker-compose up -d n8n
    Write-Host "Waiting for n8n to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}

# Check if LocalTunnel is installed
if (-not (Test-Path ".\node_modules\.bin\lt")) {
    Write-Host "❌ LocalTunnel not found. Installing..." -ForegroundColor Red
    npm install localtunnel
}

Write-Host "🚀 Starting LocalTunnel..." -ForegroundColor Green
Write-Host "OAuth Callback URLs:" -ForegroundColor Cyan
Write-Host "  Twitter: $PUBLIC_URL/rest/oauth2-credential/callback" -ForegroundColor White
Write-Host "  Google:  $PUBLIC_URL/rest/oauth2-credential/callback" -ForegroundColor White
Write-Host "  GitHub:  $PUBLIC_URL/rest/oauth2-credential/callback" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Keep this window open to maintain tunnel" -ForegroundColor Yellow

# Start LocalTunnel
& ".\node_modules\.bin\lt" --port $PORT --subdomain $SUBDOMAIN
```

### Step 3: Start LocalTunnel

In your n8n-docker directory, run:

```powershell
.\Start-LocalTunnel.ps1
```

You'll see output like:
```
🌐 LocalTunnel for n8n OAuth Integrations
=========================================
Configuration:
  Local n8n: http://localhost:5678
  Public URL: https://n8n-oauth-stable.loca.lt
  OAuth Callback: https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback

🚀 Starting LocalTunnel...
your url is: https://n8n-oauth-stable.loca.lt
```

## OAuth2 Integration Steps

### Twitter OAuth2 Setup

1. **Get your callback URL**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`
2. **Go to Twitter Developer Portal**: https://developer.twitter.com/
3. **Create/Edit App**: Add the callback URL to "Callback URLs"
4. **Get credentials**: Copy Client ID and Client Secret
5. **Add to n8n**: Settings → Credentials → Create Credential → Twitter OAuth2 API

### Google OAuth2 Setup

1. **Callback URL**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback` 
2. **Google Console**: https://console.developers.google.com/
3. **OAuth consent screen**: Configure and add authorized domains
4. **Credentials**: Create OAuth 2.0 Client ID
5. **Add to n8n**: Settings → Credentials → Create Credential → Google OAuth2 API

### GitHub OAuth2 Setup

1. **Callback URL**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`
2. **GitHub Settings**: Settings → Developer settings → OAuth Apps
3. **Register new application**: Add callback URL
4. **Get credentials**: Client ID and Client Secret
5. **Add to n8n**: Settings → Credentials → Create Credential → GitHub OAuth2 API

## File Structure

After setup, your n8n-docker directory looks like:

```
n8n-docker/
├── Start-LocalTunnel.ps1       # LocalTunnel startup script
├── package.json                # Node dependencies
├── node_modules/               # LocalTunnel installation
│   └── .bin/lt                 # LocalTunnel binary
├── docker-compose.yml          # n8n Docker configuration
└── data/                       # n8n persistent data
```

## Troubleshooting

### "Tunnel already taken"
- **Cause**: Someone else is using your subdomain
- **Solution**: Edit `Start-LocalTunnel.ps1` and change `$SUBDOMAIN` to something unique

### "Connection refused"
- **Cause**: n8n is not running on port 5678
- **Solution**: Start n8n with `docker-compose up -d n8n`

### OAuth callback errors
- **Check URLs**: Ensure callback URLs match exactly in OAuth provider settings
- **Check tunnel**: Make sure LocalTunnel is still running
- **Test connection**: Visit your tunnel URL in browser

## Alternative: Manual Command
**WARNING**: Use the script above instead to prevent multiple tunnels. For reference only:
```
# DO NOT RUN THIS - Use Start-LocalTunnel.ps1 instead
# Example: npx localtunnel --port 5678 --subdomain n8n-test
```

## Summary

This LocalTunnel setup provides:
- **Free HTTPS URLs** for OAuth2 callbacks
- **Stable subdomains** for consistent integration
- **Easy restart** if tunnel disconnects
- **Full n8n integration** for workflow automation

**Next**: Configure your OAuth2 credentials in n8n using the tunnel URLs! 