# LocalTunnel for n8n OAuth Integrations

## Quick Start

### 1. Start LocalTunnel
```powershell
.\Start-LocalTunnel.ps1
```

### 2. Use OAuth2 Callback URL
```
https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback
```

## What This Does

- **Creates stable HTTPS URL** for n8n OAuth2 integrations
- **Works with n8n Docker** - automatically starts n8n if needed
- **Installs dependencies** - automatically installs LocalTunnel if missing
- **Provides OAuth2 URLs** - ready-to-use callback URLs for Twitter, Google, GitHub, etc.

## OAuth2 Service Setup

### Twitter
1. Go to: https://developer.twitter.com/
2. App Settings → Authentication settings
3. Add callback URL: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`

### Google
1. Go to: https://console.cloud.google.com/
2. APIs & Services → Credentials
3. Add redirect URI: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`

### GitHub
1. Go to: https://github.com/settings/developers
2. New OAuth App
3. Authorization callback URL: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`

## n8n Access

- **Direct**: http://localhost:5678
- **Through tunnel**: https://n8n-oauth-stable.loca.lt (requires password for browser)

## Important Notes

- **Keep tunnel running** - OAuth2 callbacks need the tunnel active
- **Browser password** - Normal for LocalTunnel, OAuth2 APIs bypass this
- **Independent of N8N_Builder** - Works with any n8n Docker setup

## Troubleshooting

- **Subdomain taken**: Script will show error, try again with different name
- **n8n not running**: Script automatically starts n8n Docker container
- **Connection issues**: Check internet connection and Docker status

## Files Created

- `package.json` - Node.js dependencies
- `node_modules/` - LocalTunnel installation
- `Start-LocalTunnel.ps1` - Main startup script

That's it! Your n8n instance now has stable OAuth2 integration capabilities. 