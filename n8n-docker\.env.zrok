# <PERSON>rok Self-Hosted Environment Configuration Template
# Copy this to .env.zrok and customize for your environment
# IMPORTANT: Change the default tokens below for security!

# Zrok Configuration
ZROK_ADMIN_TOKEN=zrok-admin-n8n-builder-2025
ZROK_FRONTEND_TOKEN=zrok-frontend-n8n-stable-url
ZROK_ENVIRONMENT_TOKEN=

# OpenZiti Configuration
ZITI_PWD=ziti-secure-n8n-2025

# Domain Configuration (customize these)
ZROK_DOMAIN=localhost
ZROK_PUBLIC_PORT=8080
ZROK_HTTPS_PORT=8443

# N8N Integration
N8N_SERVICE_URL=http://n8n:5678
ZROK_SHARE_NAME=n8n-webhooks

# Reserved Share Configuration
ZROK_RESERVED_SHARE_TOKEN=
ZROK_STABLE_URL=

# Security Settings
ZROK_ENABLE_HTTPS=false
ZROK_SSL_CERT_PATH=./ssl/cert.pem
ZROK_SSL_KEY_PATH=./ssl/key.pem

# Logging
ZROK_LOG_LEVEL=info
ZITI_LOG_LEVEL=info

# Performance Settings
ZROK_MAX_CONNECTIONS=100
ZROK_TIMEOUT=30s
