# N8N_Builder Community Edition Deployment Script
# This script prepares the repository for public release

param(
    [switch]$DryRun,
    [switch]$Execute,
    [string]$OutputDir = "N8N_Builder_Community"
)

Write-Host "🚀 N8N_Builder Community Edition Deployment" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

if (-not $DryRun -and -not $Execute) {
    Write-Host "❌ Please specify either -DryRun or -Execute" -ForegroundColor Red
    Write-Host "Usage: .\deploy_public.ps1 -DryRun    # Preview changes"
    Write-Host "       .\deploy_public.ps1 -Execute   # Execute deployment"
    exit 1
}

# Define what to include in public version
$PublicFiles = @(
    "n8n_builder/",
    "Documentation/",
    "Scripts/",
    "tests/",
    "static/",
    "n8n-docker/",
    "projects/",
    "run_public.py",
    "requirements_public.txt",
    "README_public.md",
    "setup_public.py",
    ".augment-guidelines-public",
    ".gitignore_public",
    "GETTING_STARTED.md",
    "LIGHTNING_START.md",
    "ProcessFlow.MD"
)

# Define what to exclude (private/advanced components)
$ExcludePatterns = @(
    "Self_Healer/",
    "Self-Healer/",  # Both folder naming conventions
    "KnowledgeBase/",
    "*_private*",
    "*_advanced*",
    "safe_project_analysis_*",
    "pre_commit_cleanup_summary.json",
    "healer_config.yaml",
    "knowledgebase_config.yaml",
    "cleanup_duplicate_folder.ps1",  # Cleanup script not needed in public
    "run.py",  # Use run_public.py instead
    "requirements.txt",  # Use requirements_public.txt instead
    "README.md",  # Use README_public.md instead
    "setup.py",  # Use setup_public.py instead
    ".augment-guidelines"  # Use .augment-guidelines-public instead
)

function Test-ShouldInclude {
    param($Path)
    
    # Check if explicitly included
    foreach ($include in $PublicFiles) {
        if ($Path -like $include -or $Path.StartsWith($include)) {
            # Check if excluded by pattern
            foreach ($exclude in $ExcludePatterns) {
                if ($Path -like $exclude -or $Path.StartsWith($exclude)) {
                    return $false
                }
            }
            return $true
        }
    }
    return $false
}

if ($DryRun) {
    Write-Host "🔍 DRY RUN - Preview of public deployment:" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "📁 Files/Folders to INCLUDE:" -ForegroundColor Green
    Get-ChildItem -Recurse | Where-Object { Test-ShouldInclude $_.FullName.Substring((Get-Location).Path.Length + 1) } | ForEach-Object {
        $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 1)
        Write-Host "  ✅ $relativePath" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "📁 Files/Folders to EXCLUDE:" -ForegroundColor Red
    Get-ChildItem -Recurse | Where-Object { -not (Test-ShouldInclude $_.FullName.Substring((Get-Location).Path.Length + 1)) } | ForEach-Object {
        $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 1)
        Write-Host "  ❌ $relativePath" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "📋 Files to RENAME:" -ForegroundColor Cyan
    Write-Host "  📄 run_public.py → run.py" -ForegroundColor Cyan
    Write-Host "  📄 requirements_public.txt → requirements.txt" -ForegroundColor Cyan
    Write-Host "  📄 README_public.md → README.md" -ForegroundColor Cyan
    Write-Host "  📄 setup_public.py → setup.py" -ForegroundColor Cyan
    Write-Host "  📄 .augment-guidelines-public → .augment-guidelines" -ForegroundColor Cyan
    Write-Host "  📄 .gitignore_public → .gitignore" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🎯 To execute deployment: .\deploy_public.ps1 -Execute" -ForegroundColor Yellow
}

if ($Execute) {
    Write-Host "🚀 Executing public deployment..." -ForegroundColor Green
    
    # Create output directory
    if (Test-Path $OutputDir) {
        Write-Host "🗑️ Removing existing output directory..." -ForegroundColor Yellow
        Remove-Item $OutputDir -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
    Write-Host "📁 Created output directory: $OutputDir" -ForegroundColor Green
    
    # Copy included files
    Write-Host "📋 Copying public files..." -ForegroundColor Cyan
    foreach ($file in $PublicFiles) {
        if (Test-Path $file) {
            $destination = Join-Path $OutputDir $file
            $destinationDir = Split-Path $destination -Parent
            
            if (-not (Test-Path $destinationDir)) {
                New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
            }
            
            if (Test-Path $file -PathType Container) {
                Copy-Item $file $destination -Recurse -Force
                Write-Host "  📁 Copied folder: $file" -ForegroundColor Green
            } else {
                Copy-Item $file $destination -Force
                Write-Host "  📄 Copied file: $file" -ForegroundColor Green
            }
        }
    }
    
    # Rename public files to standard names
    Write-Host "📝 Renaming public files..." -ForegroundColor Cyan
    $renames = @{
        "run_public.py" = "run.py"
        "requirements_public.txt" = "requirements.txt"
        "README_public.md" = "README.md"
        "setup_public.py" = "setup.py"
        ".augment-guidelines-public" = ".augment-guidelines"
        ".gitignore_public" = ".gitignore"
    }
    
    foreach ($rename in $renames.GetEnumerator()) {
        $source = Join-Path $OutputDir $rename.Key
        $destination = Join-Path $OutputDir $rename.Value
        
        if (Test-Path $source) {
            Move-Item $source $destination
            Write-Host "  📄 Renamed: $($rename.Key) → $($rename.Value)" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "✅ Public deployment complete!" -ForegroundColor Green
    Write-Host "📁 Output directory: $OutputDir" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎯 Next steps:" -ForegroundColor Yellow
    Write-Host "  1. Review the output directory" -ForegroundColor White
    Write-Host "  2. Initialize git repository in $OutputDir" -ForegroundColor White
    Write-Host "  3. Create public GitHub repository" -ForegroundColor White
    Write-Host "  4. Push to public repository" -ForegroundColor White
    Write-Host ""
    Write-Host "🔒 Private components (Self-Healer, KnowledgeBase) remain secure in original directory" -ForegroundColor Green
}
