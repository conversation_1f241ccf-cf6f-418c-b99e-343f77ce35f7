# Ngrok Tunnel Setup Guide

## Overview
This guide sets up stable webhook URLs for your n8n instance using pyngrok (Python ngrok wrapper). This provides persistent public URLs that forward to your local n8n server without the complexity of other solutions.

## What You Get
- **Stable URL**: `https://your-subdomain.ngrok.io` → `http://localhost:5678`
- **Persistent Subdomain**: Same URL every time (with ngrok account)
- **HTTPS Support**: Built-in SSL certificates
- **Python Integration**: Fits your existing pip-based workflow
- **Better Reliability**: More stable than free alternatives

## Prerequisites
- Python 3.7+ installed
- Virtual environment (recommended)
- n8n running on `http://localhost:5678`
- Internet connection for tunnel creation
- ngrok account (free tier available)

## Installation

### 1. Create/Activate Virtual Environment
```bash
# Create virtual environment (if not exists)
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate
```

### 2. Install pyngrok
```bash
pip install pyngrok
```

### 3. Install to requirements.txt
Add to your `requirements.txt`:
```
pyngrok>=5.2.0
```

### 4. Setup ngrok Account (Optional but Recommended)
1. Sign up at https://ngrok.com/signup
2. Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken
3. Configure auth token (one-time setup):
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

## Usage

### Quick Start
Use the provided Python script:
```bash
python start_ngrok_tunnel.py
```

### PowerShell Wrapper
Use the PowerShell script:
```powershell
.\Start-NgrokTunnel.ps1
```

### Manual Start (Python)
```python
from pyngrok import ngrok

# Start tunnel
public_tunnel = ngrok.connect(5678)
print(f"Public URL: {public_tunnel.public_url}")
```

## Configuration

### Environment Variables
The scripts support these environment variables:
- `N8N_TUNNEL_SUBDOMAIN`: Custom subdomain (requires ngrok account)
- `N8N_TUNNEL_PORT`: Override default port (5678)
- `NGROK_AUTH_TOKEN`: Your ngrok auth token

### Custom Subdomain (Requires ngrok Account)
```bash
export N8N_TUNNEL_SUBDOMAIN="my-n8n-app"
python start_ngrok_tunnel.py
```

### Configuration File
Create `ngrok_config.json`:
```json
{
    "port": 5678,
    "subdomain": "my-n8n-app",
    "region": "us",
    "auth_token": "your_auth_token_here"
}
```

## Webhook Setup in n8n

### 1. Access n8n Interface
- Local: `http://localhost:5678`
- Public: `https://your-subdomain.ngrok.io`

### 2. Configure Webhooks
When creating webhooks in n8n, use the public URL:
```
https://your-subdomain.ngrok.io/webhook/your-webhook-path
```

### 3. Test Webhook
Use the test URL format:
```
https://your-subdomain.ngrok.io/webhook-test/your-webhook-path
```

## Python Script Features

### Automatic n8n Detection
- Checks if n8n container is running
- Starts Docker services if needed
- Waits for n8n to be ready

### Health Monitoring
- Periodic tunnel health checks
- Automatic reconnection on failure
- Logging of all tunnel events

### Graceful Shutdown
- Proper cleanup on Ctrl+C
- Tunnel termination
- Log file closure

## Troubleshooting

### Common Issues

#### "Auth Token Required"
```
ERROR: Please add your auth token
```
**Solution**: Add your ngrok auth token:
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

#### "Subdomain Not Available"
```
ERROR: Subdomain 'my-app' is not available
```
**Solution**: 
1. Try a different subdomain name
2. Ensure you have an ngrok account
3. Use random subdomain (no custom name)

#### "Connection Refused"
```
ERROR: connect ECONNREFUSED 127.0.0.1:5678
```
**Solution**: Ensure n8n is running:
```bash
docker-compose up -d
```

#### "Tunnel Limit Reached"
Free ngrok accounts have tunnel limits.
**Solution**: 
1. Close existing tunnels
2. Upgrade to paid plan
3. Use ngrok kill-all command

### Checking n8n Status
```bash
# Check if n8n container is running
docker ps --filter "name=n8n"

# Check n8n logs
docker logs n8n --tail 20
```

### Testing Connection
```bash
# Test local connection
curl http://localhost:5678

# Test tunnel connection
curl https://your-subdomain.ngrok.io
```

## Security Considerations

### Access Control
- Ngrok URLs are publicly accessible
- Consider adding authentication in n8n settings
- Monitor webhook access logs
- Use ngrok's IP whitelisting (paid plans)

### Production Use
- For production, consider:
  - Ngrok paid plans for custom domains
  - Cloudflare Tunnel (free tier)
  - Custom domain with proper SSL
  - VPN-based access

## Integration with Docker

### Starting Services in Order
1. Start n8n services:
   ```bash
   docker-compose up -d
   ```

2. Wait for n8n to be ready:
   ```bash
   sleep 10
   ```

3. Start ngrok tunnel:
   ```bash
   python start_ngrok_tunnel.py
   ```

### Stopping Services
1. Stop tunnel: `Ctrl+C` in terminal
2. Stop n8n services: `docker-compose down`

## Advanced Usage

### Multiple Tunnels
```python
from pyngrok import ngrok

# n8n tunnel
n8n_tunnel = ngrok.connect(5678, subdomain="my-n8n")

# Database admin tunnel (if needed)
db_tunnel = ngrok.connect(5432, subdomain="my-db")

print(f"n8n: {n8n_tunnel.public_url}")
print(f"DB: {db_tunnel.public_url}")
```

### Custom Regions
```python
# Use different ngrok regions
tunnel = ngrok.connect(5678, region="eu")  # Europe
tunnel = ngrok.connect(5678, region="ap")  # Asia-Pacific
```

### Webhook Validation
```python
import requests

def validate_webhook(tunnel_url):
    try:
        response = requests.get(f"{tunnel_url}/healthz", timeout=5)
        return response.status_code == 200
    except:
        return False
```

## Monitoring and Logging

### Log Files
- `ngrok_tunnel.log`: Tunnel events and status
- `ngrok_health.log`: Health check results
- `ngrok_requests.log`: Webhook request logs (if enabled)

### Health Check Script
```python
import time
import requests
from datetime import datetime

def monitor_tunnel(tunnel_url, interval=30):
    while True:
        try:
            response = requests.get(tunnel_url, timeout=5)
            status = "OK" if response.status_code == 200 else "ERROR"
            print(f"{datetime.now()}: Tunnel {status}")
        except Exception as e:
            print(f"{datetime.now()}: Tunnel DOWN - {e}")
        
        time.sleep(interval)
```

## Alternative Solutions

If pyngrok doesn't meet your needs:

### Cloudflare Tunnel
- More reliable for production
- Custom domains supported
- Built-in DDoS protection
- Free tier available

### LocalTunnel (Node.js)
- No account required
- Less stable than ngrok
- Custom subdomains available

### SSH Tunneling
- Full control over tunnel
- Requires SSH server setup
- Most secure option

## Cost Considerations

### ngrok Free Tier
- 1 tunnel at a time
- Random URLs (no custom subdomain)
- 8 hours/month limit per tunnel

### ngrok Paid Plans
- Multiple simultaneous tunnels
- Custom subdomains
- No time limits
- IP whitelisting
- Custom domains

## Support

### pyngrok Issues
- GitHub: https://github.com/alexdlaird/pyngrok
- Documentation: https://pyngrok.readthedocs.io/

### ngrok Issues
- Documentation: https://ngrok.com/docs
- Support: https://ngrok.com/support

### n8n Issues
- Documentation: https://docs.n8n.io/
- Community: https://community.n8n.io/

## Changelog

### Version 1.0 (Initial Setup)
- Basic pyngrok integration
- Python automation script
- PowerShell wrapper
- Documentation and troubleshooting guide 