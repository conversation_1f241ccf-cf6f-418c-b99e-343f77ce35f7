# Zrok Controller Configuration
# This configures the main zrok controller service

# Configuration Version
version: 4

# API Configuration
api:
  host: 0.0.0.0
  port: 18080

# Database Configuration (SQLite for simplicity)
store:
  path: /etc/zrok-controller/store.db
  type: sqlite3

# OpenZiti Integration
ziti:
  api_endpoint: "https://ziti-controller:6262"
  username: "admin"
  password: "admin123"

# Registration Configuration
registration:
  registration_url_template: "http://localhost:18080/register"
  
# Limits and Quotas
limits:
  environments_per_account: 10
  shares_per_environment: 25
  reserved_shares_per_environment: 5
  unique_names_per_environment: 10
  bandwidth_per_account:
    period: 24h
    warning: 7168  # 7GB
    limit: 10240   # 10GB

# Frontend Configuration
frontends:
  - token: "zrok-frontend-token"
    z_id: "frontend"
    public_name: "public"
    url_template: "http://localhost:8080"
    reserved_domain_names:
      - "n8n-webhooks.localhost"
      - "stable.localhost"

# Metrics and Monitoring
metrics:
  enabled: true
  influx:
    url: ""
    database: "zrok"

# Email Configuration (optional)
email:
  host: ""
  port: 587
  username: ""
  password: ""
  from: "noreply@localhost"

# Admin Configuration
admin:
  secrets:
    - "zrok-admin-token"

# Maintenance
maintenance:
  registration:
    expiration_timeout: 24h
    check_frequency: 1h
  reset_password:
    expiration_timeout: 15m
    check_frequency: 15m
