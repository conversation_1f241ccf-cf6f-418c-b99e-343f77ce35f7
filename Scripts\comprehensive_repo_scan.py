#!/usr/bin/env python3
"""
Comprehensive Repository Scan for GitHub Organization Planning
=============================================================
Scans both local and GitHub repositories to gather accurate information
for planning the GitHub organization strategy.

Usage: python comprehensive_repo_scan.py [--output FILENAME] [--verbose]
"""

import os
import json
import argparse
import subprocess
import requests
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

class ComprehensiveRepoScanner:
    """Comprehensive scanner for local and GitHub repositories."""
    
    def __init__(self, output_file: str = "repo_scan_results.json", verbose: bool = False):
        self.output_file = output_file
        self.verbose = verbose
        self.scan_start_time = datetime.now()
        
        # Results structure
        self.results = {
            "scan_metadata": {
                "scan_time": self.scan_start_time.isoformat(),
                "scan_type": "comprehensive_repository_analysis",
                "local_repo_path": str(Path.cwd()),
                "github_user": "vbwyrde"
            },
            "local_repository": {},
            "github_repositories": {},
            "analysis": {},
            "recommendations": []
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        symbols = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
        symbol = symbols.get(level, "ℹ️")
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(f"[{timestamp}] {symbol} {message}")
    
    def run_command(self, command: str, cwd: Optional[str] = None) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command, shell=True, cwd=cwd, capture_output=True, text=True, timeout=60
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def scan_local_repository(self) -> Dict[str, Any]:
        """Scan local repository structure and content."""
        self.log("Scanning local repository...")
        
        local_info = {
            "repository_root": str(Path.cwd()),
            "git_info": {},
            "file_structure": {},
            "private_components": {},
            "public_components": {},
            "configuration_files": {}
        }
        
        # Git information
        git_commands = {
            "remote_url": "git config --get remote.origin.url",
            "current_branch": "git branch --show-current",
            "status": "git status --porcelain",
            "last_commit": "git log -1 --oneline"
        }
        
        for key, command in git_commands.items():
            success, stdout, stderr = self.run_command(command)
            local_info["git_info"][key] = {
                "success": success,
                "output": stdout.strip() if success else "",
                "error": stderr.strip() if stderr else ""
            }
        
        # File structure analysis
        root_files = []
        directories = []
        
        for item in Path.cwd().iterdir():
            if item.is_file():
                root_files.append({
                    "name": item.name,
                    "size": item.stat().st_size,
                    "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                })
            elif item.is_dir() and not item.name.startswith('.'):
                dir_info = {
                    "name": item.name,
                    "file_count": len(list(item.rglob('*'))) if item.exists() else 0
                }
                directories.append(dir_info)
        
        local_info["file_structure"]["root_files"] = root_files
        local_info["file_structure"]["directories"] = directories
        
        # Private component detection
        private_indicators = [
            "Self_Healer", "Self-Healer", "KnowledgeBase", "Knowledge_Base",
            "advanced_systems", "enterprise_module"
        ]
        
        found_private = []
        for indicator in private_indicators:
            if Path(indicator).exists():
                found_private.append({
                    "name": indicator,
                    "type": "directory" if Path(indicator).is_dir() else "file",
                    "exists": True
                })
        
        local_info["private_components"]["detected"] = found_private
        
        # Community Edition component files
        community_files = [
            "README_community.md", "requirements_community.txt", "run_community.py",
            "setup_community.py", "config_community.yaml", ".gitignore_community"
        ]
        
        found_community = []
        for community_file in community_files:
            if Path(community_file).exists():
                found_community.append({
                    "name": community_file,
                    "size": Path(community_file).stat().st_size,
                    "exists": True
                })

        local_info["public_components"]["community_suffix_files"] = found_community
        
        # Configuration files
        config_files = [
            "sync-public.ps1", "Scripts/public_repo_config.json",
            "detect_private_components.py", "verification_pipeline.py"
        ]
        
        found_configs = []
        for config_file in config_files:
            if Path(config_file).exists():
                found_configs.append({
                    "name": config_file,
                    "size": Path(config_file).stat().st_size,
                    "exists": True
                })
        
        local_info["configuration_files"]["separation_system"] = found_configs
        
        return local_info
    
    def scan_github_repositories(self) -> Dict[str, Any]:
        """Scan GitHub repositories using GitHub CLI or API."""
        self.log("Scanning GitHub repositories...")
        
        github_info = {
            "scan_method": "github_cli",
            "repositories": [],
            "n8n_builder_details": {},
            "knowledgebase_details": {},
            "scan_errors": []
        }
        
        # Try GitHub CLI first (with full path, then fallback)
        gh_commands = [
            '"C:\\Program Files\\GitHub CLI\\gh.exe" repo list vbwyrde --json name,description,visibility,createdAt,updatedAt,url',
            'gh repo list vbwyrde --json name,description,visibility,createdAt,updatedAt,url'
        ]

        success = False
        stdout = ""
        stderr = ""

        for cmd in gh_commands:
            success, stdout, stderr = self.run_command(cmd)
            if success:
                break
        
        if success:
            try:
                repos = json.loads(stdout)
                github_info["repositories"] = repos
                github_info["scan_method"] = "github_cli_success"
                
                # Find specific repositories
                for repo in repos:
                    if repo["name"] == "N8N_Builder":
                        github_info["n8n_builder_details"] = repo
                        
                        # Get detailed info for N8N_Builder
                        detail_success, detail_stdout, detail_stderr = self.run_command(
                            f"gh repo view vbwyrde/N8N_Builder --json defaultBranch,hasIssues,hasWiki,hasProjects,hasDiscussions"
                        )
                        if detail_success:
                            try:
                                details = json.loads(detail_stdout)
                                github_info["n8n_builder_details"].update(details)
                            except json.JSONDecodeError:
                                pass
                    
                    elif repo["name"] == "KnowledgeBase":
                        github_info["knowledgebase_details"] = repo
                
            except json.JSONDecodeError as e:
                github_info["scan_errors"].append(f"JSON decode error: {e}")
                github_info["scan_method"] = "github_cli_failed"
        else:
            github_info["scan_errors"].append(f"GitHub CLI failed: {stderr}")
            github_info["scan_method"] = "github_cli_unavailable"
        
        # If GitHub CLI failed, try manual commands
        if not success:
            # Try to get N8N_Builder info manually
            n8n_success, n8n_stdout, n8n_stderr = self.run_command("gh repo view vbwyrde/N8N_Builder")
            if n8n_success:
                github_info["n8n_builder_details"]["manual_view"] = n8n_stdout
            
            # Try to get KnowledgeBase info manually
            kb_success, kb_stdout, kb_stderr = self.run_command("gh repo view vbwyrde/KnowledgeBase")
            if kb_success:
                github_info["knowledgebase_details"]["manual_view"] = kb_stdout
            else:
                github_info["knowledgebase_details"]["access_error"] = kb_stderr
        
        return github_info
    
    def analyze_current_state(self, local_info: Dict, github_info: Dict) -> Dict[str, Any]:
        """Analyze the current state and identify issues."""
        self.log("Analyzing current state...")
        
        analysis = {
            "repository_status": {},
            "private_component_status": {},
            "separation_readiness": {},
            "github_organization_status": {},
            "critical_issues": [],
            "recommendations": []
        }
        
        # Repository status analysis
        has_n8n_builder_github = bool(github_info.get("n8n_builder_details"))
        has_knowledgebase_github = bool(github_info.get("knowledgebase_details"))
        
        analysis["repository_status"] = {
            "n8n_builder_exists_github": has_n8n_builder_github,
            "knowledgebase_exists_github": has_knowledgebase_github,
            "local_git_configured": local_info["git_info"]["remote_url"]["success"],
            "current_branch": local_info["git_info"]["current_branch"]["output"]
        }
        
        # Private component analysis
        private_components_local = len(local_info["private_components"]["detected"])
        community_files_ready = len(local_info["public_components"]["community_suffix_files"])

        analysis["private_component_status"] = {
            "private_components_detected_locally": private_components_local,
            "community_suffix_files_ready": community_files_ready,
            "separation_system_files": len(local_info["configuration_files"]["separation_system"])
        }
        
        # Separation readiness
        required_separation_files = [
            "sync-public.ps1",
            "detect_private_components.py",
            "README_community.md",
            "requirements_community.txt"
        ]
        
        separation_files_present = sum(1 for f in required_separation_files if Path(f).exists())
        
        analysis["separation_readiness"] = {
            "required_files_present": separation_files_present,
            "total_required_files": len(required_separation_files),
            "readiness_percentage": (separation_files_present / len(required_separation_files)) * 100
        }
        
        # GitHub organization status
        if has_n8n_builder_github:
            n8n_details = github_info["n8n_builder_details"]
            analysis["github_organization_status"]["n8n_builder"] = {
                "visibility": n8n_details.get("visibility", "unknown"),
                "default_branch": n8n_details.get("defaultBranch", "unknown"),
                "created_at": n8n_details.get("createdAt", "unknown"),
                "description": n8n_details.get("description", "")
            }
        
        if has_knowledgebase_github:
            kb_details = github_info["knowledgebase_details"]
            analysis["github_organization_status"]["knowledgebase"] = {
                "visibility": kb_details.get("visibility", "unknown"),
                "access_status": "accessible" if "access_error" not in kb_details else "restricted"
            }
        
        # Critical issues identification
        if has_n8n_builder_github and private_components_local > 0:
            analysis["critical_issues"].append(
                "N8N_Builder repository exists on GitHub while private components exist locally - potential leak risk"
            )
        
        if has_knowledgebase_github and github_info["knowledgebase_details"].get("visibility") == "public":
            analysis["critical_issues"].append(
                "KnowledgeBase repository appears to be public - should be private"
            )
        
        if separation_files_present < len(required_separation_files):
            analysis["critical_issues"].append(
                f"Separation system incomplete - only {separation_files_present}/{len(required_separation_files)} required files present"
            )
        
        # Recommendations
        if analysis["critical_issues"]:
            analysis["recommendations"].append("Address critical issues before proceeding with GitHub organization")
        
        if separation_files_present == len(required_separation_files):
            analysis["recommendations"].append("Separation system appears ready - can proceed with separation execution")
        
        if has_n8n_builder_github:
            analysis["recommendations"].append("Existing N8N_Builder repository detected - consider backup/archive strategy")
        
        return analysis
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive repository scan report."""
        self.log("🔍 Starting comprehensive repository scan...")
        
        # Scan local repository
        local_info = self.scan_local_repository()
        self.results["local_repository"] = local_info
        
        # Scan GitHub repositories
        github_info = self.scan_github_repositories()
        self.results["github_repositories"] = github_info
        
        # Analyze current state
        analysis = self.analyze_current_state(local_info, github_info)
        self.results["analysis"] = analysis
        
        # Update metadata
        self.results["scan_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["scan_metadata"]["duration_seconds"] = (datetime.now() - self.scan_start_time).total_seconds()
        
        # Display summary
        self.display_scan_summary()
        
        return self.results
    
    def display_scan_summary(self):
        """Display scan summary."""
        print("\n" + "="*70)
        print("🔍 COMPREHENSIVE REPOSITORY SCAN SUMMARY")
        print("="*70)
        
        analysis = self.results["analysis"]
        
        print(f"⏱️  Scan Duration: {self.results['scan_metadata']['duration_seconds']:.2f} seconds")
        print(f"📁 Local Repository: {self.results['scan_metadata']['local_repo_path']}")
        
        # Repository status
        repo_status = analysis["repository_status"]
        print(f"\n📊 Repository Status:")
        print(f"   • N8N_Builder on GitHub: {'✅' if repo_status['n8n_builder_exists_github'] else '❌'}")
        print(f"   • KnowledgeBase on GitHub: {'✅' if repo_status['knowledgebase_exists_github'] else '❌'}")
        print(f"   • Local Git Configured: {'✅' if repo_status['local_git_configured'] else '❌'}")
        print(f"   • Current Branch: {repo_status['current_branch']}")
        
        # Private components
        private_status = analysis["private_component_status"]
        print(f"\n🔒 Private Component Status:")
        print(f"   • Private Components Detected: {private_status['private_components_detected_locally']}")
        print(f"   • Community Files Ready: {private_status['community_suffix_files_ready']}")
        print(f"   • Separation System Files: {private_status['separation_system_files']}")
        
        # Separation readiness
        sep_status = analysis["separation_readiness"]
        print(f"\n🚀 Separation Readiness:")
        print(f"   • Required Files Present: {sep_status['required_files_present']}/{sep_status['total_required_files']}")
        print(f"   • Readiness: {sep_status['readiness_percentage']:.1f}%")
        
        # Critical issues
        if analysis["critical_issues"]:
            print(f"\n🚨 Critical Issues ({len(analysis['critical_issues'])}):")
            for i, issue in enumerate(analysis["critical_issues"], 1):
                print(f"   {i}. {issue}")
        else:
            print(f"\n✅ No critical issues detected")
        
        # Recommendations
        if analysis["recommendations"]:
            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(analysis["recommendations"], 1):
                print(f"   {i}. {rec}")
        
        print("="*70)
        print(f"📄 Full report saved to: {self.output_file}")
    
    def export_results(self):
        """Export scan results to JSON file."""
        with open(self.output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Comprehensive scan results exported to: {self.output_file}", "SUCCESS")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Comprehensive Repository Scan")
    parser.add_argument("--output", default="repo_scan_results.json", help="Output file")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Initialize scanner
    scanner = ComprehensiveRepoScanner(args.output, args.verbose)
    
    # Generate comprehensive report
    results = scanner.generate_comprehensive_report()
    
    # Export results
    scanner.export_results()
    
    # Exit with appropriate code based on critical issues
    critical_issues = len(results["analysis"]["critical_issues"])
    exit(0 if critical_issues == 0 else 1)

if __name__ == "__main__":
    main()
