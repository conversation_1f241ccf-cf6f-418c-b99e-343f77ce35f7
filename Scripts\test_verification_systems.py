#!/usr/bin/env python3
"""
Test Suite for Verification Systems
===================================
Comprehensive testing of all Phase 1 verification components.

Usage: python test_verification_systems.py [--verbose] [--quick]
"""

import os
import json
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class VerificationSystemTester:
    """Test suite for all verification components."""
    
    def __init__(self, verbose: bool = False, quick: bool = False):
        self.verbose = verbose
        self.quick = quick
        self.test_start_time = datetime.now()
        self.results = {
            "test_metadata": {
                "start_time": self.test_start_time.isoformat(),
                "test_mode": "quick" if quick else "comprehensive",
                "tests_run": [],
                "tests_passed": [],
                "tests_failed": []
            },
            "test_results": {}
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def run_command(self, command: str, cwd: str = None, timeout: int = 60) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return False, "", str(e)
    
    def test_detection_script_exists(self) -> Dict[str, Any]:
        """Test 1: Verify detection script exists and is executable."""
        test_name = "detection_script_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        # Check Python detection script
        python_script = Path("detect_private_components.py")
        result["details"]["python_script_exists"] = python_script.exists()
        
        # Check PowerShell detection script
        ps_script = Path("detect-private-components.ps1")
        result["details"]["powershell_script_exists"] = ps_script.exists()
        
        if python_script.exists() and ps_script.exists():
            result["success"] = True
            self.log("✅ Both detection scripts exist")
        else:
            self.log("❌ One or more detection scripts missing", "ERROR")
        
        return result
    
    def test_detection_script_syntax(self) -> Dict[str, Any]:
        """Test 2: Verify detection scripts have valid syntax."""
        test_name = "detection_script_syntax"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": True,
            "details": {}
        }
        
        # Test Python script syntax
        python_script = "detect_private_components.py"
        if Path(python_script).exists():
            success, stdout, stderr = self.run_command(f"python -m py_compile {python_script}")
            result["details"]["python_syntax_valid"] = success
            if not success:
                result["details"]["python_syntax_error"] = stderr
                result["success"] = False
                self.log(f"❌ Python script syntax error: {stderr}", "ERROR")
            else:
                self.log("✅ Python script syntax valid")
        
        # Test PowerShell script syntax (basic check)
        ps_script = "detect-private-components.ps1"
        if Path(ps_script).exists():
            # PowerShell syntax check is more complex, so we'll do a basic file read test
            try:
                with open(ps_script, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Basic checks for common PowerShell syntax issues
                    if 'param(' in content and 'function ' in content:
                        result["details"]["powershell_syntax_basic"] = True
                        self.log("✅ PowerShell script basic syntax check passed")
                    else:
                        result["details"]["powershell_syntax_basic"] = False
                        result["success"] = False
                        self.log("❌ PowerShell script missing basic structure", "ERROR")
            except Exception as e:
                result["details"]["powershell_syntax_error"] = str(e)
                result["success"] = False
                self.log(f"❌ PowerShell script read error: {e}", "ERROR")
        
        return result
    
    def test_detection_patterns(self) -> Dict[str, Any]:
        """Test 3: Verify detection patterns work correctly."""
        test_name = "detection_patterns"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        # Create test file with known private references
        test_file = Path("test_private_references.py")
        test_content = """
# Test file with private references
from Self_Healer.core.healer_manager import SelfHealerManager
from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
import KnowledgeBase
healer_config = "test"
debug_self_healer_function()
"""
        
        try:
            with open(test_file, 'w') as f:
                f.write(test_content)
            
            # Run detection on test file
            cmd = f"python detect_private_components.py --path {test_file} --output test_detection_output.json"
            success, stdout, stderr = self.run_command(cmd, timeout=30)
            
            if success and Path("test_detection_output.json").exists():
                with open("test_detection_output.json", 'r') as f:
                    detection_results = json.load(f)
                
                references_found = detection_results["scan_metadata"]["total_references"]
                result["details"]["references_detected"] = references_found
                
                if references_found >= 5:  # Should detect at least 5 references
                    result["success"] = True
                    self.log(f"✅ Detection patterns working - found {references_found} references")
                else:
                    self.log(f"❌ Detection patterns may be incomplete - only found {references_found} references", "WARNING")
            else:
                result["details"]["detection_error"] = stderr
                self.log(f"❌ Detection script execution failed: {stderr}", "ERROR")
        
        except Exception as e:
            result["details"]["test_setup_error"] = str(e)
            self.log(f"❌ Test setup failed: {e}", "ERROR")
        
        finally:
            # Cleanup test files
            for cleanup_file in ["test_private_references.py", "test_detection_output.json", "test_detection_output.md"]:
                if Path(cleanup_file).exists():
                    Path(cleanup_file).unlink()
        
        return result
    
    def test_verification_pipeline_exists(self) -> Dict[str, Any]:
        """Test 4: Verify verification pipeline script exists."""
        test_name = "verification_pipeline_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        pipeline_script = Path("verification_pipeline.py")
        result["details"]["pipeline_script_exists"] = pipeline_script.exists()
        
        if pipeline_script.exists():
            # Check if script has main components
            with open(pipeline_script, 'r') as f:
                content = f.read()
                
            required_components = [
                "class VerificationPipeline",
                "def stage_pre_sync_detection",
                "def stage_post_sync_detection",
                "def run_pipeline"
            ]
            
            components_found = []
            for component in required_components:
                if component in content:
                    components_found.append(component)
            
            result["details"]["required_components"] = len(components_found)
            result["details"]["total_required"] = len(required_components)
            
            if len(components_found) == len(required_components):
                result["success"] = True
                self.log("✅ Verification pipeline script has all required components")
            else:
                self.log(f"❌ Verification pipeline missing components: {set(required_components) - set(components_found)}", "ERROR")
        else:
            self.log("❌ Verification pipeline script not found", "ERROR")
        
        return result
    
    def test_manual_checklist_exists(self) -> Dict[str, Any]:
        """Test 5: Verify manual review checklist exists."""
        test_name = "manual_checklist_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        checklist_file = Path("Documentation/MANUAL_REVIEW_CHECKLIST.md")
        result["details"]["checklist_exists"] = checklist_file.exists()
        
        if checklist_file.exists():
            with open(checklist_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for key sections
            required_sections = [
                "Private Component References",
                "File Structure Verification", 
                "Database and Storage References",
                "Integration Points",
                "Dependencies and Requirements",
                "Functionality Testing"
            ]
            
            sections_found = []
            for section in required_sections:
                if section in content:
                    sections_found.append(section)
            
            result["details"]["sections_found"] = len(sections_found)
            result["details"]["total_sections"] = len(required_sections)
            
            if len(sections_found) >= len(required_sections) * 0.8:  # At least 80% of sections
                result["success"] = True
                self.log("✅ Manual review checklist has comprehensive coverage")
            else:
                self.log(f"❌ Manual review checklist missing key sections", "WARNING")
        else:
            self.log("❌ Manual review checklist not found", "ERROR")
        
        return result
    
    def test_sync_script_compatibility(self) -> Dict[str, Any]:
        """Test 6: Verify sync script exists and is compatible."""
        test_name = "sync_script_compatibility"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        sync_script = Path("sync-public.ps1")
        result["details"]["sync_script_exists"] = sync_script.exists()
        
        if sync_script.exists():
            with open(sync_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for key components
            required_components = [
                "PublicSuffixFiles",
                "PublicDirectories", 
                "ExcludePatterns",
                "Clean-PrivateReferences"
            ]
            
            components_found = []
            for component in required_components:
                if component in content:
                    components_found.append(component)
            
            result["details"]["components_found"] = len(components_found)
            result["details"]["total_components"] = len(required_components)
            
            if len(components_found) == len(required_components):
                result["success"] = True
                self.log("✅ Sync script has all required components")
            else:
                self.log(f"❌ Sync script missing components", "WARNING")
        else:
            self.log("❌ Sync script not found", "ERROR")
        
        return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all verification system tests."""
        self.log("🧪 Starting Verification Systems Test Suite")
        
        test_methods = [
            self.test_detection_script_exists,
            self.test_detection_script_syntax,
            self.test_detection_patterns,
            self.test_verification_pipeline_exists,
            self.test_manual_checklist_exists,
            self.test_sync_script_compatibility
        ]
        
        if self.quick:
            # Skip the more intensive tests in quick mode
            test_methods = test_methods[:2] + test_methods[3:]
            self.log("ℹ️ Running in quick mode - skipping intensive tests")
        
        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result["test_name"]
                
                self.results["test_results"][test_name] = test_result
                self.results["test_metadata"]["tests_run"].append(test_name)
                
                if test_result["success"]:
                    self.results["test_metadata"]["tests_passed"].append(test_name)
                else:
                    self.results["test_metadata"]["tests_failed"].append(test_name)
            
            except Exception as e:
                self.log(f"❌ Test crashed: {test_method.__name__} - {e}", "ERROR")
                self.results["test_metadata"]["tests_failed"].append(test_method.__name__)
        
        # Update final metadata
        self.results["test_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["test_metadata"]["duration_seconds"] = (datetime.now() - self.test_start_time).total_seconds()
        
        # Display summary
        self.display_test_summary()
        
        return self.results
    
    def display_test_summary(self):
        """Display test execution summary."""
        print("\n" + "="*50)
        print("🧪 VERIFICATION SYSTEMS TEST SUMMARY")
        print("="*50)
        
        metadata = self.results["test_metadata"]
        total_tests = len(metadata["tests_run"])
        passed_tests = len(metadata["tests_passed"])
        failed_tests = len(metadata["tests_failed"])
        
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"🧪 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "📊 Success Rate: 0%")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Verification systems are ready for use")
        else:
            print(f"\n⚠️  {failed_tests} TESTS FAILED")
            print("❌ Address issues before proceeding with GitHub organization")
            
            if metadata["tests_failed"]:
                print(f"\n📋 Failed Tests:")
                for test in metadata["tests_failed"]:
                    print(f"   - {test}")
        
        print("="*50)
    
    def export_results(self, output_file: str = "verification_systems_test_results.json"):
        """Export test results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Test results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Test Verification Systems")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--quick", action="store_true", help="Quick test mode (skip intensive tests)")
    parser.add_argument("--output", default="verification_systems_test_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = VerificationSystemTester(args.verbose, args.quick)
    
    # Run tests
    results = tester.run_all_tests()
    
    # Export results
    tester.export_results(args.output)

if __name__ == "__main__":
    main()
