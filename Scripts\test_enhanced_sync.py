#!/usr/bin/env python3
"""
Enhanced Sync Process Test Suite
================================
Comprehensive testing of Phase 2 enhanced sync components.

Usage: python test_enhanced_sync.py [--verbose] [--quick]
"""

import os
import json
import argparse
import subprocess
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class EnhancedSyncTester:
    """Test suite for enhanced sync process components."""
    
    def __init__(self, verbose: bool = False, quick: bool = False):
        self.verbose = verbose
        self.quick = quick
        self.test_start_time = datetime.now()
        self.temp_dir = None
        self.results = {
            "test_metadata": {
                "start_time": self.test_start_time.isoformat(),
                "test_mode": "quick" if quick else "comprehensive",
                "tests_run": [],
                "tests_passed": [],
                "tests_failed": []
            },
            "test_results": {}
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def run_command(self, command: str, cwd: str = None, timeout: int = 60) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return False, "", str(e)
    
    def setup_test_environment(self):
        """Set up temporary test environment."""
        self.temp_dir = Path(tempfile.mkdtemp(prefix="n8n_builder_sync_test_"))
        self.log(f"Created test environment: {self.temp_dir}")
        return self.temp_dir
    
    def cleanup_test_environment(self):
        """Clean up temporary test environment."""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            self.log(f"Cleaned up test environment: {self.temp_dir}")
    
    def test_enhanced_sync_script_exists(self) -> Dict[str, Any]:
        """Test 1: Verify enhanced sync script exists and has new features."""
        test_name = "enhanced_sync_script_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        sync_script = Path("sync-public.ps1")
        result["details"]["sync_script_exists"] = sync_script.exists()
        
        if sync_script.exists():
            with open(sync_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for enhanced features
            enhanced_features = [
                "RunVerification",
                "Invoke-PreSyncVerification",
                "Invoke-PostSyncVerification",
                "Remove-PrivateReferences",
                "New-BackupIfExists",
                "Enhanced private reference replacement"
            ]
            
            features_found = []
            for feature in enhanced_features:
                if feature in content:
                    features_found.append(feature)
            
            result["details"]["enhanced_features_found"] = len(features_found)
            result["details"]["total_enhanced_features"] = len(enhanced_features)
            result["details"]["missing_features"] = list(set(enhanced_features) - set(features_found))
            
            if len(features_found) >= len(enhanced_features) * 0.8:  # At least 80% of features
                result["success"] = True
                self.log("✅ Enhanced sync script has required features")
            else:
                self.log(f"❌ Enhanced sync script missing features: {result['details']['missing_features']}", "ERROR")
        else:
            self.log("❌ Enhanced sync script not found", "ERROR")
        
        return result
    
    def test_public_repo_config_exists(self) -> Dict[str, Any]:
        """Test 2: Verify public repository configuration exists."""
        test_name = "public_repo_config_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        config_file = Path("Scripts/public_repo_config.json")
        result["details"]["config_file_exists"] = config_file.exists()
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Check for required configuration sections
                required_sections = [
                    "public_repository_structure",
                    "folder_structure",
                    "file_transformations",
                    "documentation_updates",
                    "github_repository_settings"
                ]
                
                sections_found = []
                for section in required_sections:
                    if section in config:
                        sections_found.append(section)
                
                result["details"]["sections_found"] = len(sections_found)
                result["details"]["total_sections"] = len(required_sections)
                result["details"]["config_valid"] = True
                
                if len(sections_found) == len(required_sections):
                    result["success"] = True
                    self.log("✅ Public repository configuration is complete")
                else:
                    self.log(f"❌ Configuration missing sections: {set(required_sections) - set(sections_found)}", "ERROR")
            
            except json.JSONDecodeError as e:
                result["details"]["config_valid"] = False
                result["details"]["json_error"] = str(e)
                self.log(f"❌ Configuration file has invalid JSON: {e}", "ERROR")
        else:
            self.log("❌ Public repository configuration not found", "ERROR")
        
        return result
    
    def test_documentation_sanitizer_exists(self) -> Dict[str, Any]:
        """Test 3: Verify documentation sanitizer exists and works."""
        test_name = "documentation_sanitizer_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        sanitizer_script = Path("sanitize_documentation.py")
        result["details"]["sanitizer_exists"] = sanitizer_script.exists()
        
        if sanitizer_script.exists():
            # Test syntax
            success, stdout, stderr = self.run_command(f"python -m py_compile {sanitizer_script}")
            result["details"]["syntax_valid"] = success
            
            if success:
                # Test help output
                success, stdout, stderr = self.run_command(f"python {sanitizer_script} --help")
                result["details"]["help_works"] = success
                
                if success and "--dry-run" in stdout:
                    result["success"] = True
                    self.log("✅ Documentation sanitizer is functional")
                else:
                    self.log("❌ Documentation sanitizer help output incomplete", "ERROR")
            else:
                result["details"]["syntax_error"] = stderr
                self.log(f"❌ Documentation sanitizer syntax error: {stderr}", "ERROR")
        else:
            self.log("❌ Documentation sanitizer not found", "ERROR")
        
        return result
    
    def test_community_readme_exists(self) -> Dict[str, Any]:
        """Test 4: Verify Community Edition README exists."""
        test_name = "community_readme_exists"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        readme_file = Path("README_community.md")
        result["details"]["readme_exists"] = readme_file.exists()
        
        if readme_file.exists():
            with open(readme_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for Community Edition specific content
            community_indicators = [
                "Community Edition",
                "open-source",
                "local AI",
                "privacy",
                "Quick Start",
                "API Usage",
                "Contributing"
            ]
            
            indicators_found = []
            for indicator in community_indicators:
                if indicator.lower() in content.lower():
                    indicators_found.append(indicator)
            
            result["details"]["community_indicators_found"] = len(indicators_found)
            result["details"]["total_indicators"] = len(community_indicators)
            
            # Check that no private references exist
            private_references = ["Self-Healer", "KnowledgeBase", "Enterprise Edition", "proprietary"]
            private_found = []
            for ref in private_references:
                if ref in content:
                    private_found.append(ref)
            
            result["details"]["private_references_found"] = len(private_found)
            result["details"]["private_references"] = private_found
            
            if len(indicators_found) >= len(community_indicators) * 0.7 and len(private_found) == 0:
                result["success"] = True
                self.log("✅ Community Edition README is appropriate")
            else:
                if len(private_found) > 0:
                    self.log(f"❌ Community README contains private references: {private_found}", "ERROR")
                else:
                    self.log("❌ Community README missing key indicators", "WARNING")
        else:
            self.log("❌ Community Edition README not found", "ERROR")
        
        return result
    
    def test_sync_dry_run(self) -> Dict[str, Any]:
        """Test 5: Test sync script dry run functionality."""
        test_name = "sync_dry_run"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        if self.quick:
            result["success"] = True
            result["details"]["skipped"] = "Skipped in quick mode"
            self.log("ℹ️ Sync dry run test skipped in quick mode")
            return result
        
        # Set up test environment
        test_dir = self.setup_test_environment()
        test_public_path = test_dir / "test_public_repo"
        
        try:
            # Run sync script in dry run mode
            sync_cmd = f"powershell -ExecutionPolicy Bypass -File Scripts\\sync-public.ps1 -PublicRepoPath {test_public_path} -DryRun"
            success, stdout, stderr = self.run_command(sync_cmd, timeout=120)
            
            result["details"]["dry_run_executed"] = success
            result["details"]["stdout_length"] = len(stdout)
            result["details"]["stderr_length"] = len(stderr)
            
            if success:
                # Check that no actual files were created (dry run)
                result["details"]["no_files_created"] = not test_public_path.exists()
                
                # Check for expected dry run messages
                dry_run_indicators = ["[DRY RUN]", "Would copy", "Would create"]
                indicators_found = sum(1 for indicator in dry_run_indicators if indicator in stdout)
                result["details"]["dry_run_indicators"] = indicators_found
                
                if not test_public_path.exists() and indicators_found > 0:
                    result["success"] = True
                    self.log("✅ Sync dry run functionality works correctly")
                else:
                    self.log("❌ Sync dry run created files or missing indicators", "ERROR")
            else:
                result["details"]["error"] = stderr
                self.log(f"❌ Sync dry run failed: {stderr}", "ERROR")
        
        except Exception as e:
            result["details"]["exception"] = str(e)
            self.log(f"❌ Sync dry run test crashed: {e}", "ERROR")
        
        finally:
            self.cleanup_test_environment()
        
        return result
    
    def test_documentation_sanitization_dry_run(self) -> Dict[str, Any]:
        """Test 6: Test documentation sanitization dry run."""
        test_name = "documentation_sanitization_dry_run"
        self.log(f"Running test: {test_name}")
        
        result = {
            "test_name": test_name,
            "success": False,
            "details": {}
        }
        
        # Create test documentation with private references
        test_dir = self.setup_test_environment()
        test_doc = test_dir / "test_doc.md"
        
        try:
            # Create test document with private references
            test_content = """
# Test Documentation

This document mentions Self-Healer and KnowledgeBase components.

## Self-Healer Features
- Advanced error detection
- Automatic resolution

## KnowledgeBase Integration
- Enterprise database features
- Proprietary algorithms
"""
            
            with open(test_doc, 'w') as f:
                f.write(test_content)
            
            # Run sanitization in dry run mode
            sanitize_cmd = f"python sanitize_documentation.py --path {test_dir} --dry-run"
            success, stdout, stderr = self.run_command(sanitize_cmd, timeout=60)
            
            result["details"]["sanitization_executed"] = success
            result["details"]["stdout_length"] = len(stdout)
            
            if success:
                # Check for expected modifications in output
                modification_indicators = ["Replaced", "Removed", "modifications"]
                indicators_found = sum(1 for indicator in modification_indicators if indicator in stdout)
                result["details"]["modification_indicators"] = indicators_found
                
                # Verify original file unchanged (dry run)
                with open(test_doc, 'r') as f:
                    final_content = f.read()
                
                result["details"]["original_unchanged"] = final_content == test_content
                
                if indicators_found > 0 and final_content == test_content:
                    result["success"] = True
                    self.log("✅ Documentation sanitization dry run works correctly")
                else:
                    self.log("❌ Documentation sanitization dry run issues", "ERROR")
            else:
                result["details"]["error"] = stderr
                self.log(f"❌ Documentation sanitization failed: {stderr}", "ERROR")
        
        except Exception as e:
            result["details"]["exception"] = str(e)
            self.log(f"❌ Documentation sanitization test crashed: {e}", "ERROR")
        
        finally:
            self.cleanup_test_environment()
        
        return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all enhanced sync process tests."""
        self.log("🧪 Starting Enhanced Sync Process Test Suite")
        
        test_methods = [
            self.test_enhanced_sync_script_exists,
            self.test_public_repo_config_exists,
            self.test_documentation_sanitizer_exists,
            self.test_community_readme_exists,
            self.test_sync_dry_run,
            self.test_documentation_sanitization_dry_run
        ]
        
        if self.quick:
            # Skip intensive tests in quick mode
            test_methods = test_methods[:4]
            self.log("ℹ️ Running in quick mode - skipping intensive tests")
        
        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result["test_name"]
                
                self.results["test_results"][test_name] = test_result
                self.results["test_metadata"]["tests_run"].append(test_name)
                
                if test_result["success"]:
                    self.results["test_metadata"]["tests_passed"].append(test_name)
                else:
                    self.results["test_metadata"]["tests_failed"].append(test_name)
            
            except Exception as e:
                self.log(f"❌ Test crashed: {test_method.__name__} - {e}", "ERROR")
                self.results["test_metadata"]["tests_failed"].append(test_method.__name__)
        
        # Update final metadata
        self.results["test_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["test_metadata"]["duration_seconds"] = (datetime.now() - self.test_start_time).total_seconds()
        
        # Display summary
        self.display_test_summary()
        
        return self.results
    
    def display_test_summary(self):
        """Display test execution summary."""
        print("\n" + "="*60)
        print("🧪 ENHANCED SYNC PROCESS TEST SUMMARY")
        print("="*60)
        
        metadata = self.results["test_metadata"]
        total_tests = len(metadata["tests_run"])
        passed_tests = len(metadata["tests_passed"])
        failed_tests = len(metadata["tests_failed"])
        
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"🧪 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "📊 Success Rate: 0%")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Enhanced sync process components are ready for use")
        else:
            print(f"\n⚠️  {failed_tests} TESTS FAILED")
            print("❌ Address issues before proceeding with Phase 3")
            
            if metadata["tests_failed"]:
                print(f"\n📋 Failed Tests:")
                for test in metadata["tests_failed"]:
                    print(f"   - {test}")
        
        print("="*60)
    
    def export_results(self, output_file: str = "enhanced_sync_test_results.json"):
        """Export test results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Test results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Test Enhanced Sync Process")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--quick", action="store_true", help="Quick test mode (skip intensive tests)")
    parser.add_argument("--output", default="enhanced_sync_test_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = EnhancedSyncTester(args.verbose, args.quick)
    
    # Run tests
    results = tester.run_all_tests()
    
    # Export results
    tester.export_results(args.output)

if __name__ == "__main__":
    main()
