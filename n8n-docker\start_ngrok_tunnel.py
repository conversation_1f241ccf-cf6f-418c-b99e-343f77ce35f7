#!/usr/bin/env python3
"""
Ngrok Tunnel Startup Script for n8n Webhook Stability
Creates a stable public URL for your local n8n instance using pyngrok
"""

import os
import sys
import json
import time
import signal
import logging
import argparse
import subprocess
from datetime import datetime
from pathlib import Path

try:
    import requests
    from pyngrok import ngrok, conf
except ImportError as e:
    print(f"❌ Missing required packages: {e}")
    print("💡 Install with: pip install pyngrok requests")
    sys.exit(1)

# Configuration
DEFAULT_PORT = 5678
DEFAULT_SUBDOMAIN = "n8n-builder-stable"
N8N_CONTAINER_NAME = "n8n"
LOG_FILE = "ngrok_tunnel.log"
CONFIG_FILE = "ngrok_config.json"

# Colors for terminal output
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'

def colored_print(message, color=Colors.WHITE):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.RESET}")

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config():
    """Load configuration from file or environment variables"""
    config = {
        'port': DEFAULT_PORT,
        'subdomain': DEFAULT_SUBDOMAIN,
        'region': 'us',
        'auth_token': None
    }
    
    # Load from config file if exists
    if Path(CONFIG_FILE).exists():
        try:
            with open(CONFIG_FILE, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except json.JSONDecodeError:
            colored_print(f"⚠️  Invalid JSON in {CONFIG_FILE}, using defaults", Colors.YELLOW)
    
    # Override with environment variables
    config['port'] = int(os.getenv('N8N_TUNNEL_PORT', config['port']))
    config['subdomain'] = os.getenv('N8N_TUNNEL_SUBDOMAIN', config['subdomain'])
    config['auth_token'] = os.getenv('NGROK_AUTH_TOKEN', config['auth_token'])
    
    return config

def check_prerequisites():
    """Check if all prerequisites are met"""
    colored_print("🔍 Checking prerequisites...", Colors.CYAN)
    
    # Check if Docker is available
    try:
        subprocess.run(['docker', '--version'], check=True, 
                      capture_output=True, text=True)
        colored_print("✅ Docker is available", Colors.GREEN)
    except (subprocess.CalledProcessError, FileNotFoundError):
        colored_print("❌ Docker is not installed or not in PATH", Colors.RED)
        return False
    
    # Check if Docker is running
    try:
        subprocess.run(['docker', 'ps'], check=True, 
                      capture_output=True, text=True)
        colored_print("✅ Docker is running", Colors.GREEN)
    except subprocess.CalledProcessError:
        colored_print("❌ Docker is not running. Please start Docker Desktop.", Colors.RED)
        return False
    
    return True

def is_n8n_running(port):
    """Check if n8n is accessible on the specified port"""
    try:
        response = requests.get(f"http://localhost:{port}", timeout=5)
        return True
    except requests.RequestException:
        return False

def get_container_status(container_name):
    """Get Docker container status"""
    try:
        result = subprocess.run(
            ['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Names}}'],
            capture_output=True, text=True, check=True
        )
        return container_name in result.stdout
    except subprocess.CalledProcessError:
        return False

def start_n8n_services():
    """Start n8n Docker services if not running"""
    colored_print("🚀 Starting n8n Docker services...", Colors.CYAN)
    
    try:
        subprocess.run(['docker-compose', 'up', '-d'], check=True, 
                      capture_output=True, text=True)
        colored_print("✅ Docker services started", Colors.GREEN)
        
        # Wait for n8n to be ready
        colored_print("⏳ Waiting for n8n to be ready...", Colors.YELLOW)
        for i in range(30):  # Wait up to 30 seconds
            if is_n8n_running(DEFAULT_PORT):
                colored_print("✅ n8n is ready", Colors.GREEN)
                return True
            time.sleep(1)
        
        colored_print("❌ n8n failed to start within 30 seconds", Colors.RED)
        return False
        
    except subprocess.CalledProcessError as e:
        colored_print(f"❌ Failed to start Docker services: {e}", Colors.RED)
        return False

def check_n8n_status(port, skip_check=False):
    """Check n8n status and start if needed"""
    if skip_check:
        colored_print("⚠️  Skipping n8n status check", Colors.YELLOW)
        return True
    
    colored_print("🔍 Checking n8n status...", Colors.CYAN)
    
    # Check if container is running
    if get_container_status(N8N_CONTAINER_NAME):
        colored_print("✅ n8n container is running", Colors.GREEN)
        
        # Check if web interface is accessible
        if is_n8n_running(port):
            colored_print(f"✅ n8n web interface is accessible at http://localhost:{port}", Colors.GREEN)
            return True
        else:
            colored_print("⚠️  n8n container running but web interface not ready, waiting...", Colors.YELLOW)
            time.sleep(10)
            
            if is_n8n_running(port):
                colored_print("✅ n8n web interface is now accessible", Colors.GREEN)
                return True
            else:
                colored_print("❌ n8n web interface still not accessible", Colors.RED)
                return False
    else:
        colored_print("⚠️  n8n container not running, starting services...", Colors.YELLOW)
        return start_n8n_services()

def setup_ngrok_auth(auth_token):
    """Setup ngrok authentication if token provided"""
    if auth_token:
        try:
            conf.get_default().auth_token = auth_token
            colored_print("✅ Ngrok auth token configured", Colors.GREEN)
            return True
        except Exception as e:
            colored_print(f"❌ Failed to configure auth token: {e}", Colors.RED)
            return False
    else:
        colored_print("⚠️  No auth token provided, using free tier", Colors.YELLOW)
        return True

def start_tunnel(config, logger):
    """Start the ngrok tunnel"""
    colored_print("🚀 Starting ngrok tunnel...", Colors.CYAN)
    
    try:
        # Configure ngrok
        if not setup_ngrok_auth(config['auth_token']):
            return None
        
        # Start tunnel
        tunnel_kwargs = {
            'port': config['port'],
            'region': config['region']
        }
        
        # Add subdomain if auth token is provided
        if config['auth_token'] and config['subdomain']:
            tunnel_kwargs['subdomain'] = config['subdomain']
        
        tunnel = ngrok.connect(**tunnel_kwargs)
        
        colored_print(f"🌐 Public URL: {tunnel.public_url}", Colors.GREEN)
        colored_print(f"🔗 Local URL: http://localhost:{config['port']}", Colors.GREEN)
        colored_print("", Colors.WHITE)
        colored_print(f"📝 Webhook URLs should use: {tunnel.public_url}/webhook/your-path", Colors.CYAN)
        colored_print(f"🧪 Test webhook URLs: {tunnel.public_url}/webhook-test/your-path", Colors.CYAN)
        colored_print("", Colors.WHITE)
        
        logger.info(f"Tunnel started: {tunnel.public_url} -> localhost:{config['port']}")
        
        return tunnel
        
    except Exception as e:
        colored_print(f"❌ Failed to start tunnel: {e}", Colors.RED)
        logger.error(f"Tunnel startup failed: {e}")
        
        # Common troubleshooting
        colored_print("", Colors.WHITE)
        colored_print("🔧 Troubleshooting:", Colors.CYAN)
        colored_print("   1. Check your internet connection", Colors.YELLOW)
        colored_print("   2. Verify ngrok auth token (if using custom subdomain)", Colors.YELLOW)
        colored_print("   3. Try without custom subdomain", Colors.YELLOW)
        colored_print(f"   4. Check if port {config['port']} is available", Colors.YELLOW)
        
        return None

def monitor_tunnel(tunnel, logger, interval=30):
    """Monitor tunnel health"""
    colored_print(f"🔍 Starting tunnel monitoring (every {interval}s)...", Colors.CYAN)
    
    while True:
        try:
            # Test tunnel connectivity
            response = requests.get(tunnel.public_url, timeout=10)
            if response.status_code == 200:
                logger.info(f"Tunnel health check: OK")
            else:
                logger.warning(f"Tunnel health check: HTTP {response.status_code}")
                
        except requests.RequestException as e:
            logger.error(f"Tunnel health check failed: {e}")
            colored_print(f"⚠️  Tunnel connectivity issue: {e}", Colors.YELLOW)
        
        time.sleep(interval)

def cleanup_tunnel(tunnel, logger):
    """Cleanup tunnel on exit"""
    if tunnel:
        try:
            ngrok.disconnect(tunnel.public_url)
            colored_print("🛑 Tunnel disconnected", Colors.YELLOW)
            logger.info("Tunnel disconnected")
        except Exception as e:
            colored_print(f"⚠️  Error disconnecting tunnel: {e}", Colors.YELLOW)
            logger.error(f"Error disconnecting tunnel: {e}")
    
    try:
        ngrok.kill()
        colored_print("🛑 Ngrok process terminated", Colors.YELLOW)
        logger.info("Ngrok process terminated")
    except Exception as e:
        colored_print(f"⚠️  Error terminating ngrok: {e}", Colors.YELLOW)
        logger.error(f"Error terminating ngrok: {e}")

def signal_handler(signum, frame, tunnel, logger):
    """Handle Ctrl+C gracefully"""
    colored_print("", Colors.WHITE)
    colored_print("🛑 Received shutdown signal...", Colors.YELLOW)
    cleanup_tunnel(tunnel, logger)
    sys.exit(0)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Start ngrok tunnel for n8n')
    parser.add_argument('--port', type=int, help='Port to tunnel (default: 5678)')
    parser.add_argument('--subdomain', help='Custom subdomain (requires auth)')
    parser.add_argument('--skip-n8n-check', action='store_true', 
                       help='Skip n8n status check')
    parser.add_argument('--verbose', action='store_true', 
                       help='Enable verbose logging')
    parser.add_argument('--no-monitor', action='store_true',
                       help='Disable tunnel monitoring')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    
    colored_print("🌐 Ngrok Tunnel Startup Script for n8n", Colors.CYAN)
    colored_print("=====================================", Colors.CYAN)
    
    logger.info("Ngrok tunnel startup initiated")
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Load configuration
    config = load_config()
    
    # Override with command line arguments
    if args.port:
        config['port'] = args.port
    if args.subdomain:
        config['subdomain'] = args.subdomain
    
    colored_print("📋 Configuration:", Colors.CYAN)
    colored_print(f"   Port: {config['port']}", Colors.YELLOW)
    colored_print(f"   Subdomain: {config['subdomain']}", Colors.YELLOW)
    colored_print(f"   Region: {config['region']}", Colors.YELLOW)
    colored_print(f"   Auth Token: {'✅ Configured' if config['auth_token'] else '❌ Not configured'}", Colors.YELLOW)
    
    # Check n8n status
    if not check_n8n_status(config['port'], args.skip_n8n_check):
        colored_print("❌ Cannot proceed without n8n running", Colors.RED)
        colored_print("   Use --skip-n8n-check to bypass this check", Colors.YELLOW)
        sys.exit(1)
    
    # Start tunnel
    tunnel = start_tunnel(config, logger)
    if not tunnel:
        sys.exit(1)
    
    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, lambda s, f: signal_handler(s, f, tunnel, logger))
    signal.signal(signal.SIGTERM, lambda s, f: signal_handler(s, f, tunnel, logger))
    
    colored_print("⚠️  Keep this window open to maintain the tunnel", Colors.YELLOW)
    colored_print("   Press Ctrl+C to stop the tunnel", Colors.YELLOW)
    colored_print("", Colors.WHITE)
    
    try:
        if args.no_monitor:
            # Just keep the tunnel alive
            while True:
                time.sleep(1)
        else:
            # Monitor tunnel health
            monitor_tunnel(tunnel, logger)
            
    except KeyboardInterrupt:
        colored_print("", Colors.WHITE)
        colored_print("🛑 Shutdown requested by user", Colors.YELLOW)
    except Exception as e:
        colored_print(f"❌ Unexpected error: {e}", Colors.RED)
        logger.error(f"Unexpected error: {e}")
    finally:
        cleanup_tunnel(tunnel, logger)

if __name__ == "__main__":
    main() 