"""
Fix for the analyze_project_files.py winsockets.exe issue
This script will clean up suspicious files and provide a safe version of the analysis
"""

import os
import sys
from pathlib import Path
import shutil
import hashlib
import datetime


def clean_suspicious_files():
    """Remove suspicious websockets.exe files that shouldn't be there."""
    print("🧹 Cleaning up suspicious files...")
    
    project_root = Path.cwd()
    suspicious_files = []
    
    # Check for websockets.exe in root directory
    root_websockets = project_root / "websockets.exe"
    if root_websockets.exists():
        suspicious_files.append(root_websockets)
    
    # Check for websockets.exe in Scripts directory
    scripts_websockets = project_root / "Scripts" / "websockets.exe"
    if scripts_websockets.exists():
        suspicious_files.append(scripts_websockets)
    
    if suspicious_files:
        print(f"⚠️ Found {len(suspicious_files)} suspicious websockets.exe files:")
        
        for file_path in suspicious_files:
            try:
                size = file_path.stat().st_size
                print(f"   📄 {file_path} ({size:,} bytes)")
                
                # Create backup before deletion
                backup_name = f"{file_path.name}.backup.{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
                backup_path = file_path.parent / backup_name
                
                print(f"   📦 Creating backup: {backup_name}")
                shutil.copy2(file_path, backup_path)
                
                print(f"   🗑️ Removing suspicious file: {file_path.name}")
                file_path.unlink()
                
                print(f"   ✅ Successfully removed and backed up")
                
            except Exception as e:
                print(f"   ❌ Error processing {file_path}: {e}")
    else:
        print("✅ No suspicious websockets.exe files found")


def create_safe_analysis_script():
    """Create a safe version of the analysis script with explicit file handling."""
    
    print("\n📝 Creating safe analysis script...")
    
    safe_script_content = '''"""
Safe Project Analysis Script - Fixed version to prevent file creation issues
"""

import os
import json
import hashlib
from pathlib import Path
from collections import defaultdict
import datetime


class SafeProjectAnalyzer:
    """Safe version of project analyzer with explicit file handling."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.files_info = {}
        self.duplicates = {}
        self.obsolete_files = []
    
    def scan_files(self):
        """Scan all files in the project."""
        print("🔍 Scanning project files...")
        
        ignore_patterns = {
            '__pycache__', '.git', '.pytest_cache', 'node_modules', 
            '.vscode', '.idea', 'venv', 'env', '.env'
        }
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                # Skip ignored directories
                if any(ignore in str(file_path) for ignore in ignore_patterns):
                    continue
                
                try:
                    stat = file_path.stat()
                    relative_path = file_path.relative_to(self.project_root)
                    
                    self.files_info[str(relative_path)] = {
                        'size': stat.st_size,
                        'modified': datetime.datetime.fromtimestamp(stat.st_mtime),
                        'extension': file_path.suffix.lower(),
                        'hash': self.get_file_hash(file_path)
                    }
                except Exception as e:
                    print(f"   ⚠️ Error processing {file_path}: {e}")
        
        print(f"✅ Scanned {len(self.files_info)} files")
    
    def get_file_hash(self, file_path):
        """Get file hash safely."""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return "ERROR"
    
    def find_duplicates(self):
        """Find duplicate files by hash."""
        print("🔍 Finding duplicate files...")
        
        hash_to_files = defaultdict(list)
        for file_path, info in self.files_info.items():
            if info['hash'] != "ERROR":
                hash_to_files[info['hash']].append(file_path)
        
        self.duplicates = {h: files for h, files in hash_to_files.items() if len(files) > 1}
        print(f"✅ Found {len(self.duplicates)} duplicate groups")
    
    def generate_report(self):
        """Generate and display report."""
        total_files = len(self.files_info)
        total_size = sum(info['size'] for info in self.files_info.values())
        
        print(f"\\n📊 Project Analysis Report")
        print(f"=" * 50)
        print(f"📁 Total files: {total_files:,}")
        print(f"💾 Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        print(f"🔄 Duplicate groups: {len(self.duplicates)}")
        
        if self.duplicates:
            print(f"\\n🔄 Duplicate Files:")
            for i, (file_hash, files) in enumerate(self.duplicates.items(), 1):
                print(f"   Group {i}: {len(files)} files ({file_hash[:8]}...)")
                for file_path in files:
                    size = self.files_info[file_path]['size']
                    print(f"     - {file_path} ({size:,} bytes)")
    
    def save_safe_report(self):
        """Save report with explicit filename and error handling."""
        
        # Use explicit filename with timestamp to avoid conflicts
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"safe_project_analysis_{timestamp}.json"
        md_filename = f"safe_project_analysis_{timestamp}.md"
        
        print(f"\\n💾 Saving reports...")
        print(f"   JSON: {json_filename}")
        print(f"   Markdown: {md_filename}")
        
        # Save JSON report
        try:
            report_data = {
                'analysis_date': datetime.datetime.now().isoformat(),
                'project_root': str(self.project_root),
                'summary': {
                    'total_files': len(self.files_info),
                    'total_size': sum(info['size'] for info in self.files_info.values()),
                    'duplicate_groups': len(self.duplicates)
                },
                'files': {path: {**info, 'modified': info['modified'].isoformat()} 
                         for path, info in self.files_info.items()},
                'duplicates': self.duplicates
            }
            
            # Write JSON with explicit encoding and error handling
            json_path = Path(json_filename)
            with open(json_path, 'w', encoding='utf-8', errors='replace') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ JSON report saved: {json_path.absolute()}")
            
        except Exception as e:
            print(f"   ❌ Error saving JSON report: {e}")
        
        # Save Markdown report
        try:
            md_content = [
                "# Safe Project Analysis Report",
                f"**Generated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"**Project Root:** `{self.project_root}`",
                "",
                "## Summary",
                f"- Total Files: {len(self.files_info):,}",
                f"- Total Size: {sum(info['size'] for info in self.files_info.values()):,} bytes",
                f"- Duplicate Groups: {len(self.duplicates)}",
                ""
            ]
            
            if self.duplicates:
                md_content.append("## Duplicate Files")
                md_content.append("")
                for i, (file_hash, files) in enumerate(self.duplicates.items(), 1):
                    md_content.append(f"### Group {i} (Hash: {file_hash[:16]}...)")
                    for file_path in files:
                        size = self.files_info[file_path]['size']
                        md_content.append(f"- `{file_path}` ({size:,} bytes)")
                    md_content.append("")
            
            # Write Markdown with explicit encoding and error handling
            md_path = Path(md_filename)
            with open(md_path, 'w', encoding='utf-8', errors='replace') as f:
                f.write('\\n'.join(md_content))
            
            print(f"   ✅ Markdown report saved: {md_path.absolute()}")
            
        except Exception as e:
            print(f"   ❌ Error saving Markdown report: {e}")
    
    def run_safe_analysis(self):
        """Run the complete safe analysis."""
        print("🚀 Starting Safe Project Analysis...")
        print(f"📁 Project root: {self.project_root}")
        
        self.scan_files()
        self.find_duplicates()
        self.generate_report()
        self.save_safe_report()
        
        print(f"\\n✅ Safe analysis complete!")


def main():
    """Main function."""
    analyzer = SafeProjectAnalyzer()
    analyzer.run_safe_analysis()


if __name__ == "__main__":
    main()
'''
    
    safe_script_path = Path("safe_analyze_project_files.py")
    
    try:
        with open(safe_script_path, 'w', encoding='utf-8') as f:
            f.write(safe_script_content)
        
        print(f"✅ Created safe analysis script: {safe_script_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating safe script: {e}")
        return False


def main():
    """Main function to fix the analyze files issue."""
    print("🔧 N8N Builder - Fix Analyze Files Issue")
    print("=" * 60)
    print("This script will:")
    print("1. Clean up suspicious websockets.exe files")
    print("2. Create a safe version of the analysis script")
    print("3. Provide recommendations for preventing future issues")
    print("")
    
    # Clean up suspicious files
    clean_suspicious_files()
    
    # Create safe analysis script
    if create_safe_analysis_script():
        print("\\n🎯 Next Steps:")
        print("1. Use the safe analysis script:")
        print("   python Scripts\\safe_analyze_project_files.py")
        print("")
        print("2. Run a full system antivirus scan")
        print("3. Check Windows Event Viewer for security warnings")
        print("4. Consider adding project directory to antivirus exclusions")
        print("")
        print("5. If issues persist, the original analyze_project_files.py")
        print("   may have been compromised and should be restored from backup")
    
    print("\\n✅ Fix process complete!")


if __name__ == "__main__":
    main()
