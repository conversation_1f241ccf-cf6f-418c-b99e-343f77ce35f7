#!/usr/bin/env python3
"""
Documentation Sanitization Script for Public Repository
=======================================================
Removes all private component references from documentation files
and replaces them with appropriate Community Edition content.

Usage: python sanitize_documentation.py [--path PATH] [--output OUTPUT] [--dry-run]
"""

import os
import re
import json
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime

class DocumentationSanitizer:
    """Sanitizes documentation for public repository release."""
    
    def __init__(self, source_path: str = ".", output_path: str = None, dry_run: bool = False):
        self.source_path = Path(source_path)
        self.output_path = Path(output_path) if output_path else self.source_path / "sanitized_docs"
        self.dry_run = dry_run
        self.sanitization_start_time = datetime.now()
        
        # Load configuration
        self.config = self.load_sanitization_config()
        
        # Track changes
        self.results = {
            "sanitization_metadata": {
                "start_time": self.sanitization_start_time.isoformat(),
                "source_path": str(self.source_path),
                "output_path": str(self.output_path),
                "dry_run": dry_run
            },
            "files_processed": 0,
            "files_modified": 0,
            "modifications": {}
        }
    
    def load_sanitization_config(self) -> Dict[str, Any]:
        """Load sanitization configuration."""
        config_file = self.source_path / "Scripts" / "public_repo_config.json"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return json.load(f)
        
        # Default configuration if file doesn't exist
        return {
            "file_transformations": {
                "content_replacements": {
                    "global_patterns": {
                        "Self-Healer": "Enterprise Module",
                        "Self_Healer": "Enterprise_Module",
                        "SelfHealer": "EnterpriseModule",
                        "KnowledgeBase": "Enterprise_Database",
                        "Knowledge_Base": "Enterprise_Database"
                    }
                }
            },
            "documentation_updates": {
                "remove_references": [
                    "Self-Healer",
                    "KnowledgeBase",
                    "Enterprise Edition",
                    "Advanced features",
                    "Proprietary components"
                ],
                "replace_with_generic": {
                    "advanced error handling": "basic error handling",
                    "enterprise features": "community features",
                    "proprietary system": "open-source system"
                }
            }
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def should_process_file(self, file_path: Path) -> bool:
        """Check if file should be processed for sanitization."""
        # Only process documentation files
        doc_extensions = {'.md', '.txt', '.rst', '.html'}
        
        if file_path.suffix.lower() not in doc_extensions:
            return False
        
        # Skip certain files
        skip_patterns = [
            'MANUAL_REVIEW_CHECKLIST.md',
            'PHASE1_COMPLETION_SUMMARY.md',
            'GITHUB_ORGANIZATION_',
            'private_',
            'advanced_',
            'enterprise_'
        ]
        
        for pattern in skip_patterns:
            if pattern.lower() in file_path.name.lower():
                return False
        
        return True
    
    def sanitize_content(self, content: str, file_path: Path) -> Tuple[str, List[str]]:
        """Sanitize content by removing private references."""
        original_content = content
        modifications = []
        
        # Get replacement patterns from config
        global_patterns = self.config.get("file_transformations", {}).get("content_replacements", {}).get("global_patterns", {})
        generic_replacements = self.config.get("documentation_updates", {}).get("replace_with_generic", {})
        
        # Apply global pattern replacements
        for pattern, replacement in global_patterns.items():
            if pattern in content:
                content = content.replace(pattern, replacement)
                modifications.append(f"Replaced '{pattern}' with '{replacement}'")
        
        # Apply generic replacements
        for pattern, replacement in generic_replacements.items():
            if pattern in content:
                content = content.replace(pattern, replacement)
                modifications.append(f"Replaced '{pattern}' with '{replacement}'")
        
        # Remove specific sections that mention private components
        private_section_patterns = [
            r'## .*Self-Healer.*\n.*?(?=\n##|\n#|\Z)',
            r'### .*KnowledgeBase.*\n.*?(?=\n###|\n##|\n#|\Z)',
            r'#### .*Enterprise.*\n.*?(?=\n####|\n###|\n##|\n#|\Z)',
            r'\*\*Self-Healer.*?\*\*.*?(?=\n\n|\Z)',
            r'\*\*KnowledgeBase.*?\*\*.*?(?=\n\n|\Z)'
        ]
        
        for pattern in private_section_patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            if matches:
                content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
                modifications.append(f"Removed private section matching pattern: {pattern[:50]}...")
        
        # Remove private component links
        link_patterns = [
            r'\[.*?Self-Healer.*?\]\(.*?\)',
            r'\[.*?KnowledgeBase.*?\]\(.*?\)',
            r'\[.*?Enterprise.*?\]\(.*?\)'
        ]
        
        for pattern in link_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                content = re.sub(pattern, '', content, flags=re.IGNORECASE)
                modifications.append(f"Removed private links: {len(matches)} found")
        
        # Remove private feature lists
        feature_list_pattern = r'- \*\*.*?(Self-Healer|KnowledgeBase|Enterprise).*?\*\*.*?\n'
        matches = re.findall(feature_list_pattern, content, re.IGNORECASE)
        if matches:
            content = re.sub(feature_list_pattern, '', content, flags=re.IGNORECASE)
            modifications.append(f"Removed {len(matches)} private feature list items")
        
        # Add Community Edition disclaimers where appropriate
        if file_path.name.lower() == 'readme.md':
            if 'Community Edition' not in content and 'N8N_Builder' in content:
                # Add Community Edition badge/notice
                community_notice = "\n> **Note**: This is the Community Edition of N8N_Builder. It provides core workflow generation capabilities with local AI processing.\n"
                
                # Insert after the first heading
                heading_pattern = r'(# .*?\n)'
                if re.search(heading_pattern, content):
                    content = re.sub(heading_pattern, r'\1' + community_notice, content, count=1)
                    modifications.append("Added Community Edition notice")
        
        return content, modifications
    
    def process_file(self, file_path: Path) -> Dict[str, Any]:
        """Process a single documentation file."""
        self.results["files_processed"] += 1
        
        try:
            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Sanitize content
            sanitized_content, modifications = self.sanitize_content(original_content, file_path)
            
            # Check if content was modified
            if sanitized_content != original_content:
                self.results["files_modified"] += 1
                
                # Calculate output path
                relative_path = file_path.relative_to(self.source_path)
                output_file_path = self.output_path / relative_path
                
                if not self.dry_run:
                    # Ensure output directory exists
                    output_file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Write sanitized content
                    with open(output_file_path, 'w', encoding='utf-8') as f:
                        f.write(sanitized_content)
                
                # Record modifications
                self.results["modifications"][str(relative_path)] = {
                    "modifications": modifications,
                    "modification_count": len(modifications),
                    "output_path": str(output_file_path) if not self.dry_run else "DRY_RUN"
                }
                
                self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Sanitized: {relative_path} ({len(modifications)} changes)")
                
                return {
                    "file_path": str(relative_path),
                    "modified": True,
                    "modifications": modifications
                }
            else:
                # No changes needed - copy file as-is if not dry run
                if not self.dry_run:
                    relative_path = file_path.relative_to(self.source_path)
                    output_file_path = self.output_path / relative_path
                    output_file_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, output_file_path)
                
                return {
                    "file_path": str(file_path.relative_to(self.source_path)),
                    "modified": False,
                    "modifications": []
                }
        
        except Exception as e:
            self.log(f"Error processing {file_path}: {e}", "ERROR")
            return {
                "file_path": str(file_path.relative_to(self.source_path)),
                "modified": False,
                "error": str(e)
            }
    
    def sanitize_documentation(self) -> Dict[str, Any]:
        """Sanitize all documentation files."""
        self.log("🧹 Starting documentation sanitization...")
        self.log(f"Source: {self.source_path}")
        self.log(f"Output: {self.output_path}")
        self.log(f"Dry run: {self.dry_run}")
        
        # Find all documentation files
        doc_files = []
        for root, dirs, files in os.walk(self.source_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['venv', '__pycache__', 'cache', 'logs']]
            
            for file in files:
                file_path = Path(root) / file
                if self.should_process_file(file_path):
                    doc_files.append(file_path)
        
        self.log(f"Found {len(doc_files)} documentation files to process")
        
        # Process each file
        for file_path in doc_files:
            self.process_file(file_path)
        
        # Update final results
        self.results["sanitization_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["sanitization_metadata"]["duration_seconds"] = (datetime.now() - self.sanitization_start_time).total_seconds()
        
        # Display summary
        self.display_summary()
        
        return self.results
    
    def display_summary(self):
        """Display sanitization summary."""
        print("\n" + "="*60)
        print("🧹 DOCUMENTATION SANITIZATION SUMMARY")
        print("="*60)
        
        metadata = self.results["sanitization_metadata"]
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"📄 Files Processed: {self.results['files_processed']}")
        print(f"✏️  Files Modified: {self.results['files_modified']}")
        print(f"🔧 Total Modifications: {sum(mod['modification_count'] for mod in self.results['modifications'].values())}")
        
        if self.results['files_modified'] > 0:
            print(f"\n📋 Modified Files:")
            for file_path, mod_info in self.results['modifications'].items():
                print(f"   - {file_path} ({mod_info['modification_count']} changes)")
        
        if self.dry_run:
            print(f"\nℹ️  This was a dry run. Use without --dry-run to perform actual sanitization.")
        else:
            print(f"\n✅ Documentation sanitization completed!")
            print(f"📁 Sanitized files saved to: {self.output_path}")
        
        print("="*60)
    
    def export_results(self, output_file: str = "documentation_sanitization_results.json"):
        """Export sanitization results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Sanitization results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Documentation Sanitization for Public Repository")
    parser.add_argument("--path", default=".", help="Source path to sanitize (default: current directory)")
    parser.add_argument("--output", help="Output path for sanitized files (default: source_path/sanitized_docs)")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode - show what would be changed")
    parser.add_argument("--results", default="documentation_sanitization_results.json", help="Results output file")
    
    args = parser.parse_args()
    
    # Initialize sanitizer
    sanitizer = DocumentationSanitizer(args.path, args.output, args.dry_run)
    
    # Run sanitization
    results = sanitizer.sanitize_documentation()
    
    # Export results
    sanitizer.export_results(args.results)

if __name__ == "__main__":
    main()
