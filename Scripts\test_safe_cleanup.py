"""
Test script to validate the improved cleanup functionality
Creates test files and verifies that essential files are preserved
"""

import tempfile
import shutil
from pathlib import Path
import sys
import os

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from Scripts.pre_commit_cleanup import PreCommitCleanup


def create_test_environment():
    """Create a test environment with various file types."""
    
    # Create temporary directory
    test_dir = Path(tempfile.mkdtemp(prefix="n8n_cleanup_test_"))
    print(f"📁 Created test directory: {test_dir}")
    
    # Create test file structure
    test_files = {
        # Essential files that should NEVER be removed
        'venv/Lib/site-packages/psutil/_psutil_windows.pyd': b'fake pyd content',
        'venv/Lib/site-packages/cryptography/_rust.pyd': b'fake rust pyd',
        'venv/Scripts/python.exe': b'fake python exe',
        'venv/Scripts/pip.exe': b'fake pip exe',
        'some_package.dist-info/METADATA': 'package metadata',
        'certificates/cert.pem': 'fake certificate',
        'src/__init__.py': '# package init',
        
        # Cache files that CAN be removed
        'src/__pycache__/module.cpython-311.pyc': b'python cache',
        'tests/__pycache__/test.cpython-311.pyc': b'test cache',
        'build/temp.win-amd64/file.pyo': b'optimized python',
        
        # Temporary files that CAN be removed
        'temp_file.tmp': 'temporary content',
        'backup_file.bak': 'backup content',
        'debug_output.log': 'log content',
        'test_file.backup.20231201': 'old backup',
        
        # Analysis files that CAN be removed
        'project_analysis_report.json': '{"test": "data"}',
        'safe_project_analysis_20231201.md': '# Test Report',
        
        # Regular source files that should be preserved
        'src/main.py': 'print("hello world")',
        'tests/test_main.py': 'def test_main(): pass',
        'README.md': '# Test Project',
        'requirements.txt': 'requests>=2.0.0',
    }
    
    # Create all test files
    for file_path, content in test_files.items():
        full_path = test_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(content, str):
            full_path.write_text(content, encoding='utf-8')
        else:
            full_path.write_bytes(content)
    
    print(f"✅ Created {len(test_files)} test files")
    return test_dir, test_files


def test_cleanup_safety():
    """Test that the cleanup preserves essential files."""
    
    print("🧪 Testing Safe Cleanup Functionality")
    print("=" * 60)
    
    # Create test environment
    test_dir, original_files = create_test_environment()
    
    try:
        # Change to test directory
        original_cwd = Path.cwd()
        os.chdir(test_dir)
        
        # Create cleanup instance
        cleanup = PreCommitCleanup()
        
        # Override some settings for testing
        cleanup.project_root = test_dir
        
        print(f"\n📊 Before cleanup:")
        before_files = list(test_dir.rglob('*'))
        before_count = len([f for f in before_files if f.is_file()])
        print(f"   Total files: {before_count}")
        
        # Test essential file detection
        print(f"\n🔍 Testing essential file detection:")
        essential_files = []
        removable_files = []
        
        for file_path in test_dir.rglob('*'):
            if file_path.is_file():
                if cleanup.is_essential_file(file_path):
                    essential_files.append(file_path)
                    print(f"   🛡️ Essential: {file_path.relative_to(test_dir)}")
                else:
                    removable_files.append(file_path)
                    print(f"   🗑️ Removable: {file_path.relative_to(test_dir)}")
        
        # Verify essential files are correctly identified
        expected_essential = [
            'venv/Lib/site-packages/psutil/_psutil_windows.pyd',
            'venv/Lib/site-packages/cryptography/_rust.pyd',
            'venv/Scripts/python.exe',
            'venv/Scripts/pip.exe',
            'src/__init__.py'
        ]
        
        print(f"\n✅ Essential File Verification:")
        for expected in expected_essential:
            expected_path = test_dir / expected
            if expected_path in essential_files:
                print(f"   ✅ Correctly identified as essential: {expected}")
            else:
                print(f"   ❌ FAILED - Not identified as essential: {expected}")
        
        # Verify removable files are correctly identified
        expected_removable = [
            'src/__pycache__/module.cpython-311.pyc',
            'temp_file.tmp',
            'backup_file.bak',
            'debug_output.log'
        ]
        
        print(f"\n🗑️ Removable File Verification:")
        for expected in expected_removable:
            expected_path = test_dir / expected
            if expected_path in removable_files:
                print(f"   ✅ Correctly identified as removable: {expected}")
            else:
                print(f"   ❌ FAILED - Not identified as removable: {expected}")
        
        # Run actual cleanup (this will modify files)
        print(f"\n🧹 Running cleanup operations...")
        cleanup.clean_cache_directories()
        cleanup.clean_temporary_files()
        cleanup.clean_analysis_reports()
        
        # Check results
        print(f"\n📊 After cleanup:")
        after_files = list(test_dir.rglob('*'))
        after_count = len([f for f in after_files if f.is_file()])
        print(f"   Total files: {after_count}")
        print(f"   Files removed: {before_count - after_count}")
        print(f"   Cleanup operations: {cleanup.files_removed}")
        print(f"   Space saved: {cleanup.space_saved:,} bytes")
        
        # Verify essential files still exist
        print(f"\n🛡️ Essential File Preservation Check:")
        all_preserved = True
        for expected in expected_essential:
            expected_path = test_dir / expected
            if expected_path.exists():
                print(f"   ✅ Preserved: {expected}")
            else:
                print(f"   ❌ LOST: {expected}")
                all_preserved = False
        
        # Verify removable files were removed
        print(f"\n🗑️ Removable File Cleanup Check:")
        all_removed = True
        for expected in expected_removable:
            expected_path = test_dir / expected
            if not expected_path.exists():
                print(f"   ✅ Removed: {expected}")
            else:
                print(f"   ⚠️ Still exists: {expected}")
                # This might be OK if the file wasn't in a pattern we clean
        
        # Final assessment
        print(f"\n🎯 Test Results:")
        if all_preserved:
            print(f"   ✅ SUCCESS: All essential files preserved")
        else:
            print(f"   ❌ FAILURE: Some essential files were lost")
        
        print(f"   📊 Cleanup efficiency: {cleanup.files_removed} files removed")
        print(f"   💾 Space saved: {cleanup.space_saved/1024:.1f} KB")
        
        return all_preserved
        
    finally:
        # Cleanup test directory
        os.chdir(original_cwd)
        shutil.rmtree(test_dir)
        print(f"\n🧹 Cleaned up test directory")


def main():
    """Main test function."""
    print("🧪 Safe Cleanup Test Suite")
    print("=" * 60)
    print("This test verifies that the improved cleanup script")
    print("preserves essential files while removing unnecessary ones.")
    print()
    
    success = test_cleanup_safety()
    
    if success:
        print("\n🎉 All tests passed! The cleanup script is safe to use.")
        print("\n💡 You can now run:")
        print("   python pre_commit_cleanup.py")
        print("   (It will preserve all .pyd files and other essentials)")
    else:
        print("\n❌ Some tests failed. Review the cleanup logic before using.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
