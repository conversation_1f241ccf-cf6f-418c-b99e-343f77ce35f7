#!/usr/bin/env python3
"""
Graceful shutdown script for N8N_Builder system.
This script attempts to gracefully shutdown all components before forcing termination.
"""

import asyncio
import logging
import os
import sys
import psutil
import signal
import time
from typing import List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_n8n_builder_processes() -> List[psutil.Process]:
    """Find all processes related to N8N_Builder."""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # Check if process is related to N8N_Builder
            if any(keyword in cmdline.lower() for keyword in [
                'run.py', 'n8n_builder', 'self-healer', 'dashboard.py'
            ]):
                processes.append(proc)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return processes

def find_processes_by_ports(ports: List[int]) -> List[psutil.Process]:
    """Find processes using specific ports."""
    processes = []

    for proc in psutil.process_iter(['pid', 'name']):
        try:
            # Skip system processes that can't be terminated
            if proc.info['pid'] in [0, 4]:  # System Idle Process and System process
                continue

            # Skip if we can't access the process
            if proc.info['name'].lower() in ['system', 'system idle process']:
                continue

            connections = proc.net_connections()
            for conn in connections:
                if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port in ports:
                    # Double-check this isn't a system process
                    if proc.info['pid'] > 4:  # Only add non-system processes
                        processes.append(proc)
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue

    return processes

def graceful_shutdown(processes: List[psutil.Process], timeout: int = 10) -> bool:
    """Attempt graceful shutdown of processes."""
    if not processes:
        return True
    
    logger.info(f"Attempting graceful shutdown of {len(processes)} processes...")
    
    # Send SIGTERM to all processes
    for proc in processes:
        try:
            logger.info(f"Sending SIGTERM to {proc.name()} (PID: {proc.pid})")
            proc.terminate()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # Wait for processes to terminate
    _, alive = psutil.wait_procs(processes, timeout=timeout)

    if alive:
        logger.warning(f"{len(alive)} processes did not terminate gracefully")
        return False
    else:
        logger.info("All processes terminated gracefully")
        return True

def force_shutdown(processes: List[psutil.Process]) -> None:
    """Force shutdown of processes."""
    if not processes:
        return
    
    logger.info(f"Force killing {len(processes)} processes...")
    
    for proc in processes:
        try:
            logger.info(f"Force killing {proc.name()} (PID: {proc.pid})")
            proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # Wait a moment for processes to die
    time.sleep(2)

def cleanup_ports(ports: List[int]) -> None:
    """Clean up processes using specific ports."""
    logger.info(f"Cleaning up ports: {ports}")
    
    port_processes = find_processes_by_ports(ports)
    if port_processes:
        logger.info(f"Found {len(port_processes)} processes using target ports")
        
        # Try graceful shutdown first
        if not graceful_shutdown(port_processes, timeout=5):
            # Force shutdown if graceful fails
            force_shutdown(port_processes)
    else:
        logger.info("No processes found using target ports")

async def shutdown_knowledge_cache():
    """Shutdown knowledge cache if available."""
    try:
        from n8n_builder.knowledge_cache import EnhancedKnowledgeCache
        import gc
        
        # Find any active cache instances and shut them down
        for obj in gc.get_objects():
            if isinstance(obj, EnhancedKnowledgeCache):
                obj.shutdown()
        
        logger.info("Knowledge cache shutdown complete")
    except Exception as e:
        logger.debug(f"Knowledge cache shutdown: {e}")

def main():
    """Main shutdown function."""
    logger.info("Starting N8N_Builder system shutdown...")
    
    # Define ports used by N8N_Builder
    target_ports = [8002, 8003, 8081]
    
    try:
        # Find N8N_Builder processes
        n8n_processes = find_n8n_builder_processes()
        
        if n8n_processes:
            logger.info(f"Found {len(n8n_processes)} N8N_Builder processes")
            
            # Try graceful shutdown first
            if not graceful_shutdown(n8n_processes, timeout=15):
                # Force shutdown if graceful fails
                logger.warning("Graceful shutdown failed, forcing shutdown...")
                force_shutdown(n8n_processes)
        else:
            logger.info("No N8N_Builder processes found")
        
        # Clean up any processes still using our ports
        cleanup_ports(target_ports)
        
        # Shutdown knowledge cache
        asyncio.run(shutdown_knowledge_cache())
        
        # Verify ports are free
        logger.info("Verifying ports are free...")
        remaining_processes = find_processes_by_ports(target_ports)
        if remaining_processes:
            logger.warning(f"{len(remaining_processes)} processes still using target ports")
            for proc in remaining_processes:
                logger.warning(f"  - {proc.name()} (PID: {proc.pid})")
        else:
            logger.info("All target ports are free")
        
        logger.info("System shutdown complete!")
        
    except Exception as e:
        logger.error(f"Shutdown failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
