#!/usr/bin/env python3
"""
Documentation Link Validator

Validates all internal markdown links in the cleaned documentation structure
to ensure no broken references remain after the consolidation.

Author: N8N_Builder Development Team
Date: 2025-07-08
Purpose: Validate Step 4 of documentation consolidation
"""

import os
import re
from pathlib import Path
from datetime import datetime

def find_markdown_files():
    """Find all remaining markdown files"""
    md_files = []
    
    # Define the files we expect to exist
    expected_files = [
        'README.md',
        'GETTING_STARTED.md', 
        'FEATURES.md',
        'Documentation/Architecture.md',
        'Documentation/DesignPrinciples.md',
        'Documentation/DevelopersWorkflow.md',
        'Documentation/guides/Integration.md',
        'Documentation/guides/Troubleshooting.md',
        'Documentation/technical/Specifications.md',
        'Documentation/api/API_Reference.md',
        'Scripts/README.md'
    ]
    
    for file_path in expected_files:
        full_path = Path(file_path)
        if full_path.exists():
            md_files.append(str(full_path))
        else:
            print(f"⚠️  Expected file not found: {file_path}")
    
    return md_files

def extract_markdown_links(file_path):
    """Extract all markdown links from a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return [], f"Error reading file: {e}"
    
    # Find all markdown links [text](url)
    link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    links = re.findall(link_pattern, content)
    
    # Filter for .md links (internal documentation links)
    md_links = []
    for text, url in links:
        if url.endswith('.md') and not url.startswith('http'):
            md_links.append({'text': text, 'url': url, 'line': None})
    
    return md_links, None

def validate_link(source_file, link_url):
    """Validate that a link target exists"""
    source_path = Path(source_file)
    
    # Handle relative paths
    if link_url.startswith('../'):
        # Go up from source file directory
        target_path = source_path.parent / link_url
    elif link_url.startswith('./'):
        # Same directory as source file
        target_path = source_path.parent / link_url[2:]
    else:
        # Relative to source file directory
        target_path = source_path.parent / link_url
    
    # Resolve the path
    try:
        resolved_path = target_path.resolve()
        return resolved_path.exists(), str(resolved_path)
    except Exception as e:
        return False, f"Error resolving path: {e}"

def validate_all_links():
    """Validate all links in all markdown files"""
    print("N8N_Builder Documentation Link Validator")
    print("=" * 45)
    
    md_files = find_markdown_files()
    print(f"Found {len(md_files)} markdown files to validate\n")
    
    all_links = []
    broken_links = []
    
    for file_path in md_files:
        print(f"📄 Checking: {file_path}")
        links, error = extract_markdown_links(file_path)
        
        if error:
            print(f"   ❌ {error}")
            continue
        
        if not links:
            print(f"   ✅ No internal .md links found")
            continue
        
        print(f"   🔗 Found {len(links)} internal links")
        
        for link in links:
            all_links.append({
                'source_file': file_path,
                'link_text': link['text'],
                'link_url': link['url']
            })
            
            exists, resolved_path = validate_link(file_path, link['url'])
            if exists:
                print(f"      ✅ {link['text']} → {link['url']}")
            else:
                print(f"      ❌ {link['text']} → {link['url']} (NOT FOUND)")
                broken_links.append({
                    'source_file': file_path,
                    'link_text': link['text'],
                    'link_url': link['url'],
                    'resolved_path': resolved_path
                })
        
        print()
    
    # Summary
    print("📊 Validation Summary")
    print("-" * 25)
    print(f"Total files checked: {len(md_files)}")
    print(f"Total internal links: {len(all_links)}")
    print(f"Broken links: {len(broken_links)}")
    
    if broken_links:
        print(f"\n❌ Found {len(broken_links)} broken links:")
        for broken in broken_links:
            print(f"   • {broken['source_file']}: '{broken['link_text']}' → {broken['link_url']}")
        return False
    else:
        print(f"\n✅ All links are valid!")
        return True

def generate_link_report():
    """Generate a comprehensive link validation report"""
    md_files = find_markdown_files()
    
    report_file = "data/link_validation_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# Documentation Link Validation Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 📊 Summary\n\n")
        f.write(f"- **Files Validated:** {len(md_files)}\n")
        
        total_links = 0
        broken_count = 0
        
        f.write("\n## 📄 File Details\n\n")
        
        for file_path in md_files:
            f.write(f"### {file_path}\n\n")
            links, error = extract_markdown_links(file_path)
            
            if error:
                f.write(f"❌ Error: {error}\n\n")
                continue
            
            if not links:
                f.write("✅ No internal .md links found\n\n")
                continue
            
            f.write(f"Found {len(links)} internal links:\n\n")
            total_links += len(links)
            
            for link in links:
                exists, resolved_path = validate_link(file_path, link['url'])
                status = "✅" if exists else "❌"
                if not exists:
                    broken_count += 1
                f.write(f"- {status} [{link['text']}]({link['url']})\n")
            
            f.write("\n")
        
        f.write(f"## 📈 Final Results\n\n")
        f.write(f"- **Total Internal Links:** {total_links}\n")
        f.write(f"- **Broken Links:** {broken_count}\n")
        f.write(f"- **Success Rate:** {((total_links - broken_count) / total_links * 100) if total_links > 0 else 100:.1f}%\n")
    
    return report_file

def main():
    """Main execution function"""
    # Validate all links
    all_valid = validate_all_links()
    
    # Generate detailed report
    report_file = generate_link_report()
    print(f"\n📋 Detailed report saved to: {report_file}")
    
    if all_valid:
        print("\n🎉 Documentation link validation PASSED!")
        return 0
    else:
        print("\n⚠️  Documentation link validation FAILED!")
        return 1

if __name__ == "__main__":
    exit(main())
