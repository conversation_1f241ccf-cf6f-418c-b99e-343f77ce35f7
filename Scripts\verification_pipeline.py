#!/usr/bin/env python3
"""
Automated Verification Pipeline for GitHub Organization
======================================================
Multi-stage verification process for public/private repository separation.

Usage: python verification_pipeline.py [--stage STAGE] [--config CONFIG] [--verbose]
"""

import os
import json
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
import shutil

class VerificationPipeline:
    """Multi-stage verification system for repository separation."""
    
    def __init__(self, config_file: str = None, verbose: bool = False):
        self.verbose = verbose
        self.config = self.load_config(config_file)
        self.verification_start_time = datetime.now()
        self.results = {
            "pipeline_metadata": {
                "start_time": self.verification_start_time.isoformat(),
                "config_file": config_file,
                "stages_completed": [],
                "stages_failed": []
            },
            "stage_results": {}
        }
    
    def load_config(self, config_file: str = None) -> Dict[str, Any]:
        """Load verification configuration."""
        default_config = {
            "private_repo_path": ".",
            "public_repo_path": "../N8N_Builder_Community",
            "sync_script": "sync-public.ps1",
            "detection_script": "detect_private_components.py",
            "verification_stages": [
                "pre_sync_detection",
                "pre_sync_validation", 
                "sync_execution",
                "post_sync_detection",
                "cross_reference_check",
                "functionality_test"
            ],
            "critical_files": [
                "run_public.py",
                "requirements_public.txt",
                "README_public.md",
                "config_public.yaml"
            ],
            "test_commands": [
                "python -m pytest tests/ -v --tb=short",
                "python run_public.py --test-mode"
            ]
        }
        
        if config_file and Path(config_file).exists():
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def run_command(self, command: str, cwd: str = None) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Command timed out"
        except Exception as e:
            return False, "", str(e)
    
    def stage_pre_sync_detection(self) -> Dict[str, Any]:
        """Stage 1: Pre-sync private component detection."""
        self.log("=== STAGE 1: Pre-sync Private Component Detection ===")
        
        stage_result = {
            "stage_name": "pre_sync_detection",
            "success": False,
            "findings": {},
            "recommendations": []
        }
        
        # Run detection script on private repository
        detection_cmd = f"python {self.config['detection_script']} --path {self.config['private_repo_path']} --output pre_sync_detection.json --markdown"
        success, stdout, stderr = self.run_command(detection_cmd)
        
        if success:
            self.log("✅ Pre-sync detection completed successfully")
            
            # Load detection results
            if Path("pre_sync_detection.json").exists():
                with open("pre_sync_detection.json", 'r') as f:
                    detection_results = json.load(f)
                
                stage_result["findings"] = detection_results
                
                if detection_results["scan_metadata"]["files_with_references"] > 0:
                    self.log(f"⚠️ Found {detection_results['scan_metadata']['total_references']} private references in {detection_results['scan_metadata']['files_with_references']} files", "WARNING")
                    stage_result["recommendations"].append("Clean private references before proceeding with sync")
                    stage_result["success"] = False
                else:
                    self.log("✅ No private references detected - ready for sync")
                    stage_result["success"] = True
            else:
                self.log("❌ Detection report file not found", "ERROR")
                stage_result["success"] = False
        else:
            self.log(f"❌ Pre-sync detection failed: {stderr}", "ERROR")
            stage_result["success"] = False
        
        return stage_result
    
    def stage_pre_sync_validation(self) -> Dict[str, Any]:
        """Stage 2: Pre-sync validation of critical files."""
        self.log("=== STAGE 2: Pre-sync Validation ===")
        
        stage_result = {
            "stage_name": "pre_sync_validation",
            "success": True,
            "missing_files": [],
            "file_checks": {}
        }
        
        # Check for critical public files
        for file_path in self.config["critical_files"]:
            file_exists = Path(file_path).exists()
            stage_result["file_checks"][file_path] = file_exists
            
            if file_exists:
                self.log(f"✅ Critical file found: {file_path}")
            else:
                self.log(f"❌ Missing critical file: {file_path}", "ERROR")
                stage_result["missing_files"].append(file_path)
                stage_result["success"] = False
        
        if stage_result["success"]:
            self.log("✅ All critical files present")
        else:
            self.log(f"❌ Missing {len(stage_result['missing_files'])} critical files", "ERROR")
        
        return stage_result
    
    def stage_sync_execution(self) -> Dict[str, Any]:
        """Stage 3: Execute sync process."""
        self.log("=== STAGE 3: Sync Execution ===")
        
        stage_result = {
            "stage_name": "sync_execution",
            "success": False,
            "sync_output": "",
            "sync_errors": ""
        }
        
        # Execute sync script
        sync_cmd = f"powershell -ExecutionPolicy Bypass -File {self.config['sync_script']} -PublicRepoPath {self.config['public_repo_path']} -Force"
        success, stdout, stderr = self.run_command(sync_cmd)
        
        stage_result["sync_output"] = stdout
        stage_result["sync_errors"] = stderr
        
        if success:
            self.log("✅ Sync execution completed successfully")
            stage_result["success"] = True
        else:
            self.log(f"❌ Sync execution failed: {stderr}", "ERROR")
            stage_result["success"] = False
        
        return stage_result
    
    def stage_post_sync_detection(self) -> Dict[str, Any]:
        """Stage 4: Post-sync private component detection."""
        self.log("=== STAGE 4: Post-sync Private Component Detection ===")
        
        stage_result = {
            "stage_name": "post_sync_detection",
            "success": False,
            "findings": {},
            "leaked_components": []
        }
        
        if not Path(self.config["public_repo_path"]).exists():
            self.log("❌ Public repository path does not exist", "ERROR")
            stage_result["success"] = False
            return stage_result
        
        # Run detection script on public repository
        detection_cmd = f"python {self.config['detection_script']} --path {self.config['public_repo_path']} --output post_sync_detection.json --markdown"
        success, stdout, stderr = self.run_command(detection_cmd)
        
        if success:
            self.log("✅ Post-sync detection completed successfully")
            
            # Load detection results
            if Path("post_sync_detection.json").exists():
                with open("post_sync_detection.json", 'r') as f:
                    detection_results = json.load(f)
                
                stage_result["findings"] = detection_results
                
                if detection_results["scan_metadata"]["files_with_references"] > 0:
                    self.log(f"❌ PRIVATE COMPONENT LEAK DETECTED! Found {detection_results['scan_metadata']['total_references']} references", "ERROR")
                    stage_result["leaked_components"] = list(detection_results["results"].keys())
                    stage_result["success"] = False
                else:
                    self.log("✅ No private components detected in public repository")
                    stage_result["success"] = True
            else:
                self.log("❌ Post-sync detection report not found", "ERROR")
                stage_result["success"] = False
        else:
            self.log(f"❌ Post-sync detection failed: {stderr}", "ERROR")
            stage_result["success"] = False
        
        return stage_result
    
    def stage_cross_reference_check(self) -> Dict[str, Any]:
        """Stage 5: Cross-reference verification."""
        self.log("=== STAGE 5: Cross-reference Verification ===")
        
        stage_result = {
            "stage_name": "cross_reference_check",
            "success": True,
            "broken_links": [],
            "missing_references": []
        }
        
        # This is a placeholder for more sophisticated link checking
        # In a full implementation, this would:
        # 1. Parse all documentation files
        # 2. Check internal links and references
        # 3. Verify import statements work
        # 4. Check configuration file references
        
        self.log("✅ Cross-reference check completed (placeholder)")
        
        return stage_result
    
    def stage_functionality_test(self) -> Dict[str, Any]:
        """Stage 6: Functionality testing."""
        self.log("=== STAGE 6: Functionality Testing ===")
        
        stage_result = {
            "stage_name": "functionality_test",
            "success": True,
            "test_results": {},
            "failed_tests": []
        }
        
        # Run test commands
        for test_cmd in self.config["test_commands"]:
            self.log(f"Running test: {test_cmd}")
            success, stdout, stderr = self.run_command(test_cmd, self.config["public_repo_path"])
            
            stage_result["test_results"][test_cmd] = {
                "success": success,
                "stdout": stdout,
                "stderr": stderr
            }
            
            if success:
                self.log(f"✅ Test passed: {test_cmd}")
            else:
                self.log(f"❌ Test failed: {test_cmd}", "ERROR")
                stage_result["failed_tests"].append(test_cmd)
                stage_result["success"] = False
        
        if stage_result["success"]:
            self.log("✅ All functionality tests passed")
        else:
            self.log(f"❌ {len(stage_result['failed_tests'])} tests failed", "ERROR")
        
        return stage_result
    
    def run_pipeline(self, stages: List[str] = None) -> Dict[str, Any]:
        """Run the complete verification pipeline."""
        if stages is None:
            stages = self.config["verification_stages"]
        
        self.log("🚀 Starting Verification Pipeline")
        self.log(f"Stages to run: {', '.join(stages)}")
        
        stage_methods = {
            "pre_sync_detection": self.stage_pre_sync_detection,
            "pre_sync_validation": self.stage_pre_sync_validation,
            "sync_execution": self.stage_sync_execution,
            "post_sync_detection": self.stage_post_sync_detection,
            "cross_reference_check": self.stage_cross_reference_check,
            "functionality_test": self.stage_functionality_test
        }
        
        pipeline_success = True
        
        for stage in stages:
            if stage in stage_methods:
                try:
                    stage_result = stage_methods[stage]()
                    self.results["stage_results"][stage] = stage_result
                    
                    if stage_result["success"]:
                        self.results["pipeline_metadata"]["stages_completed"].append(stage)
                        self.log(f"✅ Stage '{stage}' completed successfully", "SUCCESS")
                    else:
                        self.results["pipeline_metadata"]["stages_failed"].append(stage)
                        self.log(f"❌ Stage '{stage}' failed", "ERROR")
                        pipeline_success = False
                        
                        # Stop pipeline on critical failures
                        if stage in ["pre_sync_detection", "post_sync_detection"]:
                            self.log("🛑 Critical stage failed - stopping pipeline", "ERROR")
                            break
                
                except Exception as e:
                    self.log(f"❌ Stage '{stage}' crashed: {e}", "ERROR")
                    self.results["pipeline_metadata"]["stages_failed"].append(stage)
                    pipeline_success = False
                    break
            else:
                self.log(f"❌ Unknown stage: {stage}", "ERROR")
                pipeline_success = False
        
        # Update final results
        self.results["pipeline_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["pipeline_metadata"]["duration_seconds"] = (datetime.now() - self.verification_start_time).total_seconds()
        self.results["pipeline_metadata"]["overall_success"] = pipeline_success
        
        # Display final summary
        self.display_pipeline_summary()
        
        return self.results
    
    def display_pipeline_summary(self):
        """Display pipeline execution summary."""
        print("\n" + "="*60)
        print("🔍 VERIFICATION PIPELINE SUMMARY")
        print("="*60)
        
        metadata = self.results["pipeline_metadata"]
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"✅ Completed Stages: {len(metadata['stages_completed'])}")
        print(f"❌ Failed Stages: {len(metadata['stages_failed'])}")
        
        if metadata["overall_success"]:
            print("\n🎉 PIPELINE SUCCESS!")
            print("✅ All verification stages passed")
            print("🚀 Repository is ready for public sync")
        else:
            print("\n⚠️  PIPELINE FAILED!")
            print("❌ One or more verification stages failed")
            print("🔧 Review stage results and address issues before proceeding")
            
            if metadata["stages_failed"]:
                print(f"\n📋 Failed Stages:")
                for stage in metadata["stages_failed"]:
                    print(f"   - {stage}")
        
        print("="*60)
    
    def export_results(self, output_file: str = "verification_pipeline_results.json"):
        """Export pipeline results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Pipeline results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Automated Verification Pipeline")
    parser.add_argument("--stage", help="Run specific stage only")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--output", default="verification_pipeline_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = VerificationPipeline(args.config, args.verbose)
    
    # Run pipeline
    stages = [args.stage] if args.stage else None
    results = pipeline.run_pipeline(stages)
    
    # Export results
    pipeline.export_results(args.output)

if __name__ == "__main__":
    main()
